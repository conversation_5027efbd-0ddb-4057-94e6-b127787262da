{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\EmployeeManagementSystem\\\\frontend\\\\src\\\\pages\\\\Dashboard.js\",\n  _s = $RefreshSig$();\nimport { Business as BusinessIcon, CheckCircle as CheckCircleIcon, Description as DocumentIcon, LocationOn as LocationIcon, Pending as PendingIcon, People as PeopleIcon } from '@mui/icons-material';\nimport { Avatar, Box, Card, CardContent, Grid, Typography } from '@mui/material';\nimport { useCallback, useContext, useEffect, useState } from 'react';\nimport { AuthContext } from '../context/AuthContext';\nimport { documentService, employeeService, locationService } from '../services/api';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  const {\n    user,\n    isAdmin\n  } = useContext(AuthContext);\n  const [stats, setStats] = useState({\n    totalEmployees: 0,\n    totalDocuments: 0,\n    pendingDocuments: 0,\n    verifiedDocuments: 0,\n    rejectedDocuments: 0,\n    totalLocations: 0\n  });\n  const [loading, setLoading] = useState(true);\n  const [checkInStatus, setCheckInStatus] = useState({\n    isCheckedIn: false,\n    checkInTime: null,\n    currentLocation: null\n  });\n  const fetchDashboardData = useCallback(async () => {\n    try {\n      setLoading(true);\n      if (isAdmin) {\n        // Fetch admin dashboard data\n        const [employeesRes, documentsRes, locationsRes] = await Promise.all([employeeService.getAllEmployees(), documentService.getAllDocuments(), locationService.getAllLocations()]);\n        const documents = documentsRes.data.data || [];\n        const pendingDocs = documents.filter(doc => doc.verificationStatus === 'pending').length;\n        const verifiedDocs = documents.filter(doc => doc.verificationStatus === 'verified').length;\n        const rejectedDocs = documents.filter(doc => doc.verificationStatus === 'rejected').length;\n        setStats({\n          totalEmployees: employeesRes.data.count || 0,\n          totalDocuments: documents.length,\n          pendingDocuments: pendingDocs,\n          verifiedDocuments: verifiedDocs,\n          rejectedDocuments: rejectedDocs,\n          totalLocations: locationsRes.data.count || 0\n        });\n      } else {\n        // Fetch employee dashboard data\n        const [documentsRes, locationsRes] = await Promise.all([documentService.getAllDocuments(), locationService.getAllLocations()]);\n        const documents = documentsRes.data.data || [];\n        const pendingDocs = documents.filter(doc => doc.verificationStatus === 'pending').length;\n        const verifiedDocs = documents.filter(doc => doc.verificationStatus === 'verified').length;\n        const rejectedDocs = documents.filter(doc => doc.verificationStatus === 'rejected').length;\n\n        // Check if employee is currently checked in\n        const locations = locationsRes.data.data || [];\n        console.log('Dashboard: Checking locations for user:', user.id);\n        console.log('Dashboard: All locations:', locations);\n        const currentCheckIn = locations.find(loc => {\n          const isMatch = loc.employee && (loc.employee._id === user.id || loc.employee.user === user.id) && loc.status === 'checked-in';\n          console.log('Dashboard: Checking location:', loc, 'Match:', isMatch);\n          return isMatch;\n        });\n        setCheckInStatus({\n          isCheckedIn: !!currentCheckIn,\n          checkInTime: (currentCheckIn === null || currentCheckIn === void 0 ? void 0 : currentCheckIn.checkInTime) || null,\n          currentLocation: (currentCheckIn === null || currentCheckIn === void 0 ? void 0 : currentCheckIn.address) || null\n        });\n        setStats({\n          totalEmployees: 0,\n          totalDocuments: documents.length,\n          pendingDocuments: pendingDocs,\n          verifiedDocuments: verifiedDocs,\n          rejectedDocuments: rejectedDocs,\n          totalLocations: locationsRes.data.count || 0\n        });\n      }\n    } catch (error) {\n      console.error('Error fetching dashboard data:', error);\n    } finally {\n      setLoading(false);\n    }\n  }, [isAdmin, user]);\n  useEffect(() => {\n    fetchDashboardData();\n  }, [fetchDashboardData]);\n\n  // Listen for location status changes\n  useEffect(() => {\n    const handleLocationStatusChange = () => {\n      console.log('Location status changed, refreshing dashboard...');\n      fetchDashboardData();\n    };\n\n    // Listen for custom location status change events\n    window.addEventListener('locationStatusChanged', handleLocationStatusChange);\n\n    // Listen for storage changes (for cross-tab communication)\n    const handleStorageChange = e => {\n      if (e.key === 'locationStatusChanged') {\n        handleLocationStatusChange();\n      }\n    };\n    window.addEventListener('storage', handleStorageChange);\n    return () => {\n      window.removeEventListener('locationStatusChanged', handleLocationStatusChange);\n      window.removeEventListener('storage', handleStorageChange);\n    };\n  }, [fetchDashboardData]);\n\n  // Polling mechanism as fallback for real-time updates (for employees only)\n  useEffect(() => {\n    if (!isAdmin) {\n      const pollInterval = setInterval(() => {\n        console.log('Dashboard: Polling for status updates...');\n        fetchDashboardData();\n      }, 30000); // Poll every 30 seconds\n\n      return () => clearInterval(pollInterval);\n    }\n  }, [isAdmin, fetchDashboardData]);\n  const StatCard = ({\n    title,\n    value,\n    icon,\n    color,\n    subtitle\n  }) => /*#__PURE__*/_jsxDEV(Card, {\n    sx: {\n      height: '100%',\n      backgroundColor: '#ffffff',\n      border: '1px solid #e2e8f0',\n      borderRadius: 2,\n      transition: 'all 0.2s ease-in-out',\n      '&:hover': {\n        transform: 'translateY(-2px)',\n        boxShadow: '0 4px 12px rgba(0,0,0,0.1)',\n        borderColor: color\n      }\n    },\n    children: /*#__PURE__*/_jsxDEV(CardContent, {\n      sx: {\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Avatar, {\n          sx: {\n            bgcolor: color,\n            width: 48,\n            height: 48,\n            mr: 2\n          },\n          children: icon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            flex: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              color: '#64748b',\n              fontWeight: 500,\n              mb: 0.5\n            },\n            children: title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              fontWeight: 700,\n              color: '#1e293b',\n              fontSize: '1.25rem'\n            },\n            children: loading ? '...' : value\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"caption\",\n        sx: {\n          color: '#94a3b8'\n        },\n        children: subtitle\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 165,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 151,\n    columnNumber: 5\n  }, this);\n  const QuickActionCard = ({\n    title,\n    description,\n    icon,\n    color,\n    action\n  }) => /*#__PURE__*/_jsxDEV(Card, {\n    sx: {\n      height: '100%',\n      cursor: 'pointer',\n      backgroundColor: '#ffffff',\n      border: '1px solid #e2e8f0',\n      borderRadius: 2,\n      transition: 'all 0.2s ease-in-out',\n      '&:hover': {\n        transform: 'translateY(-2px)',\n        boxShadow: '0 4px 12px rgba(0,0,0,0.1)',\n        borderColor: color\n      }\n    },\n    onClick: action,\n    children: /*#__PURE__*/_jsxDEV(CardContent, {\n      sx: {\n        p: 3,\n        textAlign: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Avatar, {\n        sx: {\n          bgcolor: color,\n          color: '#ffffff',\n          width: 56,\n          height: 56,\n          mx: 'auto',\n          mb: 2\n        },\n        children: icon\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        sx: {\n          fontWeight: 600,\n          mb: 1,\n          color: '#1e293b'\n        },\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        sx: {\n          color: '#64748b'\n        },\n        children: description\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 226,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 210,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 194,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      width: '100%'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: {\n          xs: 'h5',\n          sm: 'h4'\n        },\n        sx: {\n          fontWeight: 700,\n          color: '#1e293b',\n          mb: 1,\n          fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, sans-serif'\n        },\n        children: [\"Welcome back, \", user === null || user === void 0 ? void 0 : user.name, \"! \\uD83D\\uDC4B\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 237,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        sx: {\n          color: '#64748b',\n          fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, sans-serif'\n        },\n        children: isAdmin ? 'Here\\'s what\\'s happening with your team today.' : 'Here\\'s your personal dashboard overview.'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 236,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      sx: {\n        mb: 4\n      },\n      children: [isAdmin && /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Total Employees\",\n          value: stats.totalEmployees,\n          icon: /*#__PURE__*/_jsxDEV(PeopleIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 23\n          }, this),\n          color: \"#1e40af\",\n          subtitle: \"Active staff members\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 261,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Total Documents\",\n          value: stats.totalDocuments,\n          icon: /*#__PURE__*/_jsxDEV(DocumentIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 21\n          }, this),\n          color: \"#059669\",\n          subtitle: isAdmin ? \"All documents\" : \"My documents\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Pending Review\",\n          value: stats.pendingDocuments,\n          icon: /*#__PURE__*/_jsxDEV(PendingIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 21\n          }, this),\n          color: \"#d97706\",\n          subtitle: \"Awaiting approval\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Verified Documents\",\n          value: stats.verifiedDocuments,\n          icon: /*#__PURE__*/_jsxDEV(CheckCircleIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 21\n          }, this),\n          color: \"#16a34a\",\n          subtitle: \"Approved documents\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 289,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 259,\n      columnNumber: 7\n    }, this), !isAdmin && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          background: checkInStatus.isCheckedIn ? 'linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%)' : 'linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%)',\n          border: checkInStatus.isCheckedIn ? '2px solid #22c55e' : '2px solid #94a3b8',\n          borderRadius: 3,\n          transition: 'all 0.3s ease-in-out',\n          '&:hover': {\n            transform: 'translateY(-2px)',\n            boxShadow: checkInStatus.isCheckedIn ? '0 8px 25px rgba(34, 197, 94, 0.2)' : '0 8px 25px rgba(0, 0, 0, 0.1)'\n          }\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          sx: {\n            p: 3\n          },\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'space-between'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                sx: {\n                  bgcolor: checkInStatus.isCheckedIn ? '#22c55e' : '#64748b',\n                  width: 56,\n                  height: 56,\n                  mr: 3,\n                  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'\n                },\n                children: /*#__PURE__*/_jsxDEV(LocationIcon, {\n                  sx: {\n                    fontSize: 28\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 334,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 325,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  sx: {\n                    fontWeight: 700,\n                    color: checkInStatus.isCheckedIn ? '#166534' : '#475569',\n                    mb: 0.5,\n                    fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, sans-serif'\n                  },\n                  children: checkInStatus.isCheckedIn ? '✅ Checked In' : '⏰ Not Checked In'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 337,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    color: checkInStatus.isCheckedIn ? '#15803d' : '#64748b',\n                    fontWeight: 500\n                  },\n                  children: checkInStatus.isCheckedIn ? `Since: ${new Date(checkInStatus.checkInTime).toLocaleString()}` : 'Ready to start your work day?'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 348,\n                  columnNumber: 21\n                }, this), checkInStatus.isCheckedIn && checkInStatus.currentLocation && /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  sx: {\n                    color: '#059669',\n                    fontWeight: 500,\n                    display: 'block',\n                    mt: 0.5\n                  },\n                  children: [\"\\uD83D\\uDCCD \", checkInStatus.currentLocation.length > 50 ? checkInStatus.currentLocation.substring(0, 50) + '...' : checkInStatus.currentLocation]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 361,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                textAlign: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                sx: {\n                  color: checkInStatus.isCheckedIn ? '#166534' : '#64748b',\n                  fontWeight: 600,\n                  textTransform: 'uppercase',\n                  letterSpacing: '0.05em'\n                },\n                children: \"Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 379,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                sx: {\n                  fontWeight: 800,\n                  color: checkInStatus.isCheckedIn ? '#22c55e' : '#94a3b8',\n                  lineHeight: 1\n                },\n                children: checkInStatus.isCheckedIn ? '🟢' : '🔴'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 390,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 322,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 304,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 303,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h5\",\n        sx: {\n          fontWeight: 600,\n          mb: 3,\n          color: '#1e293b',\n          fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, sans-serif'\n        },\n        children: \"Quick Actions\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 409,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: isAdmin ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(QuickActionCard, {\n              title: \"Manage Employees\",\n              description: \"Add, edit, or remove employee records\",\n              icon: /*#__PURE__*/_jsxDEV(PeopleIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 427,\n                columnNumber: 27\n              }, this),\n              color: \"#1e40af\",\n              action: () => window.location.href = '/employees'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 424,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(QuickActionCard, {\n              title: \"Review Documents\",\n              description: \"Verify and approve employee documents\",\n              icon: /*#__PURE__*/_jsxDEV(DocumentIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 436,\n                columnNumber: 27\n              }, this),\n              color: \"#059669\",\n              action: () => window.location.href = '/documents'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 433,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 432,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(QuickActionCard, {\n              title: \"Location Tracking\",\n              description: \"Monitor employee locations and check-ins\",\n              icon: /*#__PURE__*/_jsxDEV(LocationIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 445,\n                columnNumber: 27\n              }, this),\n              color: \"#d97706\",\n              action: () => window.location.href = '/locations'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 442,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 441,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(QuickActionCard, {\n              title: \"Upload Documents\",\n              description: \"Submit your documents for verification\",\n              icon: /*#__PURE__*/_jsxDEV(DocumentIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 457,\n                columnNumber: 27\n              }, this),\n              color: \"#059669\",\n              action: () => window.location.href = '/documents'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 454,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 453,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(QuickActionCard, {\n              title: checkInStatus.isCheckedIn ? \"Check Out\" : \"Check In\",\n              description: checkInStatus.isCheckedIn ? \"End your work session\" : \"Start your work session\",\n              icon: /*#__PURE__*/_jsxDEV(LocationIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 466,\n                columnNumber: 27\n              }, this),\n              color: checkInStatus.isCheckedIn ? \"#dc2626\" : \"#34d399\",\n              action: () => window.location.href = '/locations'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 463,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 462,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(QuickActionCard, {\n              title: \"My Profile\",\n              description: \"View and update your profile information\",\n              icon: /*#__PURE__*/_jsxDEV(BusinessIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 475,\n                columnNumber: 27\n              }, this),\n              color: \"#1e40af\",\n              action: () => window.location.href = '/profile'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 472,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 471,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 420,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 408,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 234,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"HNsShalm6NspYXVVNOoiZOZycRE=\");\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["Business", "BusinessIcon", "CheckCircle", "CheckCircleIcon", "Description", "DocumentIcon", "LocationOn", "LocationIcon", "Pending", "PendingIcon", "People", "PeopleIcon", "Avatar", "Box", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Grid", "Typography", "useCallback", "useContext", "useEffect", "useState", "AuthContext", "documentService", "employeeService", "locationService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Dashboard", "_s", "user", "isAdmin", "stats", "setStats", "totalEmployees", "totalDocuments", "pendingDocuments", "verifiedDocuments", "rejectedDocuments", "totalLocations", "loading", "setLoading", "checkInStatus", "setCheckInStatus", "isCheckedIn", "checkInTime", "currentLocation", "fetchDashboardData", "employeesRes", "documentsRes", "locationsRes", "Promise", "all", "getAllEmployees", "getAllDocuments", "getAllLocations", "documents", "data", "pendingDocs", "filter", "doc", "verificationStatus", "length", "verifiedDocs", "rejectedDocs", "count", "locations", "console", "log", "id", "currentCheckIn", "find", "loc", "isMatch", "employee", "_id", "status", "address", "error", "handleLocationStatusChange", "window", "addEventListener", "handleStorageChange", "e", "key", "removeEventListener", "pollInterval", "setInterval", "clearInterval", "StatCard", "title", "value", "icon", "color", "subtitle", "sx", "height", "backgroundColor", "border", "borderRadius", "transition", "transform", "boxShadow", "borderColor", "children", "p", "display", "alignItems", "mb", "bgcolor", "width", "mr", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "flex", "variant", "fontWeight", "fontSize", "QuickActionCard", "description", "action", "cursor", "onClick", "textAlign", "mx", "xs", "sm", "fontFamily", "name", "container", "spacing", "item", "md", "background", "justifyContent", "Date", "toLocaleString", "mt", "substring", "textTransform", "letterSpacing", "lineHeight", "location", "href", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/EmployeeManagementSystem/frontend/src/pages/Dashboard.js"], "sourcesContent": ["import {\n  Business as BusinessIcon,\n  CheckCircle as CheckCircleIcon,\n  Description as DocumentIcon,\n  LocationOn as LocationIcon,\n  Pending as PendingIcon,\n  People as PeopleIcon\n} from '@mui/icons-material';\nimport {\n  Avatar,\n  Box,\n  Card,\n  CardContent,\n  Grid,\n  Typography\n} from '@mui/material';\nimport { useCallback, useContext, useEffect, useState } from 'react';\nimport { AuthContext } from '../context/AuthContext';\nimport { documentService, employeeService, locationService } from '../services/api';\n\nconst Dashboard = () => {\n  const { user, isAdmin } = useContext(AuthContext);\n  const [stats, setStats] = useState({\n    totalEmployees: 0,\n    totalDocuments: 0,\n    pendingDocuments: 0,\n    verifiedDocuments: 0,\n    rejectedDocuments: 0,\n    totalLocations: 0\n  });\n  const [loading, setLoading] = useState(true);\n  const [checkInStatus, setCheckInStatus] = useState({\n    isCheckedIn: false,\n    checkInTime: null,\n    currentLocation: null\n  });\n\n  const fetchDashboardData = useCallback(async () => {\n    try {\n      setLoading(true);\n\n      if (isAdmin) {\n        // Fetch admin dashboard data\n        const [employeesRes, documentsRes, locationsRes] = await Promise.all([\n          employeeService.getAllEmployees(),\n          documentService.getAllDocuments(),\n          locationService.getAllLocations()\n        ]);\n\n        const documents = documentsRes.data.data || [];\n        const pendingDocs = documents.filter(doc => doc.verificationStatus === 'pending').length;\n        const verifiedDocs = documents.filter(doc => doc.verificationStatus === 'verified').length;\n        const rejectedDocs = documents.filter(doc => doc.verificationStatus === 'rejected').length;\n\n        setStats({\n          totalEmployees: employeesRes.data.count || 0,\n          totalDocuments: documents.length,\n          pendingDocuments: pendingDocs,\n          verifiedDocuments: verifiedDocs,\n          rejectedDocuments: rejectedDocs,\n          totalLocations: locationsRes.data.count || 0\n        });\n      } else {\n        // Fetch employee dashboard data\n        const [documentsRes, locationsRes] = await Promise.all([\n          documentService.getAllDocuments(),\n          locationService.getAllLocations()\n        ]);\n\n        const documents = documentsRes.data.data || [];\n        const pendingDocs = documents.filter(doc => doc.verificationStatus === 'pending').length;\n        const verifiedDocs = documents.filter(doc => doc.verificationStatus === 'verified').length;\n        const rejectedDocs = documents.filter(doc => doc.verificationStatus === 'rejected').length;\n\n        // Check if employee is currently checked in\n        const locations = locationsRes.data.data || [];\n        console.log('Dashboard: Checking locations for user:', user.id);\n        console.log('Dashboard: All locations:', locations);\n\n        const currentCheckIn = locations.find(loc => {\n          const isMatch = loc.employee &&\n                         (loc.employee._id === user.id || loc.employee.user === user.id) &&\n                         loc.status === 'checked-in';\n          console.log('Dashboard: Checking location:', loc, 'Match:', isMatch);\n          return isMatch;\n        });\n\n        setCheckInStatus({\n          isCheckedIn: !!currentCheckIn,\n          checkInTime: currentCheckIn?.checkInTime || null,\n          currentLocation: currentCheckIn?.address || null\n        });\n\n        setStats({\n          totalEmployees: 0,\n          totalDocuments: documents.length,\n          pendingDocuments: pendingDocs,\n          verifiedDocuments: verifiedDocs,\n          rejectedDocuments: rejectedDocs,\n          totalLocations: locationsRes.data.count || 0\n        });\n      }\n    } catch (error) {\n      console.error('Error fetching dashboard data:', error);\n    } finally {\n      setLoading(false);\n    }\n  }, [isAdmin, user]);\n\n  useEffect(() => {\n    fetchDashboardData();\n  }, [fetchDashboardData]);\n\n  // Listen for location status changes\n  useEffect(() => {\n    const handleLocationStatusChange = () => {\n      console.log('Location status changed, refreshing dashboard...');\n      fetchDashboardData();\n    };\n\n    // Listen for custom location status change events\n    window.addEventListener('locationStatusChanged', handleLocationStatusChange);\n\n    // Listen for storage changes (for cross-tab communication)\n    const handleStorageChange = (e) => {\n      if (e.key === 'locationStatusChanged') {\n        handleLocationStatusChange();\n      }\n    };\n    window.addEventListener('storage', handleStorageChange);\n\n    return () => {\n      window.removeEventListener('locationStatusChanged', handleLocationStatusChange);\n      window.removeEventListener('storage', handleStorageChange);\n    };\n  }, [fetchDashboardData]);\n\n  // Polling mechanism as fallback for real-time updates (for employees only)\n  useEffect(() => {\n    if (!isAdmin) {\n      const pollInterval = setInterval(() => {\n        console.log('Dashboard: Polling for status updates...');\n        fetchDashboardData();\n      }, 30000); // Poll every 30 seconds\n\n      return () => clearInterval(pollInterval);\n    }\n  }, [isAdmin, fetchDashboardData]);\n\n  const StatCard = ({ title, value, icon, color, subtitle }) => (\n    <Card\n      sx={{\n        height: '100%',\n        backgroundColor: '#ffffff',\n        border: '1px solid #e2e8f0',\n        borderRadius: 2,\n        transition: 'all 0.2s ease-in-out',\n        '&:hover': {\n          transform: 'translateY(-2px)',\n          boxShadow: '0 4px 12px rgba(0,0,0,0.1)',\n          borderColor: color,\n        }\n      }}\n    >\n      <CardContent sx={{ p: 3 }}>\n        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n          <Avatar\n            sx={{\n              bgcolor: color,\n              width: 48,\n              height: 48,\n              mr: 2\n            }}\n          >\n            {icon}\n          </Avatar>\n          <Box sx={{ flex: 1 }}>\n            <Typography variant=\"body2\" sx={{ color: '#64748b', fontWeight: 500, mb: 0.5 }}>\n              {title}\n            </Typography>\n            <Typography variant=\"h6\" sx={{ fontWeight: 700, color: '#1e293b', fontSize: '1.25rem' }}>\n              {loading ? '...' : value}\n            </Typography>\n          </Box>\n        </Box>\n        <Typography variant=\"caption\" sx={{ color: '#94a3b8' }}>\n          {subtitle}\n        </Typography>\n      </CardContent>\n    </Card>\n  );\n\n  const QuickActionCard = ({ title, description, icon, color, action }) => (\n    <Card\n      sx={{\n        height: '100%',\n        cursor: 'pointer',\n        backgroundColor: '#ffffff',\n        border: '1px solid #e2e8f0',\n        borderRadius: 2,\n        transition: 'all 0.2s ease-in-out',\n        '&:hover': {\n          transform: 'translateY(-2px)',\n          boxShadow: '0 4px 12px rgba(0,0,0,0.1)',\n          borderColor: color,\n        }\n      }}\n      onClick={action}\n    >\n      <CardContent sx={{ p: 3, textAlign: 'center' }}>\n        <Avatar\n          sx={{\n            bgcolor: color,\n            color: '#ffffff',\n            width: 56,\n            height: 56,\n            mx: 'auto',\n            mb: 2\n          }}\n        >\n          {icon}\n        </Avatar>\n        <Typography variant=\"h6\" sx={{ fontWeight: 600, mb: 1, color: '#1e293b' }}>\n          {title}\n        </Typography>\n        <Typography variant=\"body2\" sx={{ color: '#64748b' }}>\n          {description}\n        </Typography>\n      </CardContent>\n    </Card>\n  );\n\n  return (\n    <Box sx={{ width: '100%' }}>\n      {/* Welcome Section */}\n      <Box sx={{ mb: 4 }}>\n        <Typography\n          variant={{ xs: 'h5', sm: 'h4' }}\n          sx={{\n            fontWeight: 700,\n            color: '#1e293b',\n            mb: 1,\n            fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, sans-serif'\n          }}\n        >\n          Welcome back, {user?.name}! 👋\n        </Typography>\n        <Typography\n          variant=\"body1\"\n          sx={{\n            color: '#64748b',\n            fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, sans-serif'\n          }}\n        >\n          {isAdmin ? 'Here\\'s what\\'s happening with your team today.' : 'Here\\'s your personal dashboard overview.'}\n        </Typography>\n      </Box>\n      {/* Statistics Cards */}\n      <Grid container spacing={3} sx={{ mb: 4 }}>\n          {isAdmin && (\n            <Grid item xs={12} sm={6} md={3}>\n              <StatCard\n                title=\"Total Employees\"\n                value={stats.totalEmployees}\n                icon={<PeopleIcon />}\n                color=\"#1e40af\"\n                subtitle=\"Active staff members\"\n              />\n            </Grid>\n          )}\n          <Grid item xs={12} sm={6} md={3}>\n            <StatCard\n              title=\"Total Documents\"\n              value={stats.totalDocuments}\n              icon={<DocumentIcon />}\n              color=\"#059669\"\n              subtitle={isAdmin ? \"All documents\" : \"My documents\"}\n            />\n          </Grid>\n          <Grid item xs={12} sm={6} md={3}>\n            <StatCard\n              title=\"Pending Review\"\n              value={stats.pendingDocuments}\n              icon={<PendingIcon />}\n              color=\"#d97706\"\n              subtitle=\"Awaiting approval\"\n            />\n          </Grid>\n          <Grid item xs={12} sm={6} md={3}>\n            <StatCard\n              title=\"Verified Documents\"\n              value={stats.verifiedDocuments}\n              icon={<CheckCircleIcon />}\n              color=\"#16a34a\"\n              subtitle=\"Approved documents\"\n            />\n          </Grid>\n\n        </Grid>\n\n      {/* Check-in Status for Employees */}\n      {!isAdmin && (\n        <Box sx={{ mb: 4 }}>\n          <Card\n            sx={{\n              background: checkInStatus.isCheckedIn\n                ? 'linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%)'\n                : 'linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%)',\n              border: checkInStatus.isCheckedIn\n                ? '2px solid #22c55e'\n                : '2px solid #94a3b8',\n              borderRadius: 3,\n              transition: 'all 0.3s ease-in-out',\n              '&:hover': {\n                transform: 'translateY(-2px)',\n                boxShadow: checkInStatus.isCheckedIn\n                  ? '0 8px 25px rgba(34, 197, 94, 0.2)'\n                  : '0 8px 25px rgba(0, 0, 0, 0.1)',\n              }\n            }}\n          >\n            <CardContent sx={{ p: 3 }}>\n              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                  <Avatar\n                    sx={{\n                      bgcolor: checkInStatus.isCheckedIn ? '#22c55e' : '#64748b',\n                      width: 56,\n                      height: 56,\n                      mr: 3,\n                      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'\n                    }}\n                  >\n                    <LocationIcon sx={{ fontSize: 28 }} />\n                  </Avatar>\n                  <Box>\n                    <Typography\n                      variant=\"h6\"\n                      sx={{\n                        fontWeight: 700,\n                        color: checkInStatus.isCheckedIn ? '#166534' : '#475569',\n                        mb: 0.5,\n                        fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, sans-serif'\n                      }}\n                    >\n                      {checkInStatus.isCheckedIn ? '✅ Checked In' : '⏰ Not Checked In'}\n                    </Typography>\n                    <Typography\n                      variant=\"body2\"\n                      sx={{\n                        color: checkInStatus.isCheckedIn ? '#15803d' : '#64748b',\n                        fontWeight: 500\n                      }}\n                    >\n                      {checkInStatus.isCheckedIn\n                        ? `Since: ${new Date(checkInStatus.checkInTime).toLocaleString()}`\n                        : 'Ready to start your work day?'\n                      }\n                    </Typography>\n                    {checkInStatus.isCheckedIn && checkInStatus.currentLocation && (\n                      <Typography\n                        variant=\"caption\"\n                        sx={{\n                          color: '#059669',\n                          fontWeight: 500,\n                          display: 'block',\n                          mt: 0.5\n                        }}\n                      >\n                        📍 {checkInStatus.currentLocation.length > 50\n                          ? checkInStatus.currentLocation.substring(0, 50) + '...'\n                          : checkInStatus.currentLocation\n                        }\n                      </Typography>\n                    )}\n                  </Box>\n                </Box>\n                <Box sx={{ textAlign: 'center' }}>\n                  <Typography\n                    variant=\"caption\"\n                    sx={{\n                      color: checkInStatus.isCheckedIn ? '#166534' : '#64748b',\n                      fontWeight: 600,\n                      textTransform: 'uppercase',\n                      letterSpacing: '0.05em'\n                    }}\n                  >\n                    Status\n                  </Typography>\n                  <Typography\n                    variant=\"h4\"\n                    sx={{\n                      fontWeight: 800,\n                      color: checkInStatus.isCheckedIn ? '#22c55e' : '#94a3b8',\n                      lineHeight: 1\n                    }}\n                  >\n                    {checkInStatus.isCheckedIn ? '🟢' : '🔴'}\n                  </Typography>\n                </Box>\n              </Box>\n            </CardContent>\n          </Card>\n        </Box>\n      )}\n\n      {/* Quick Actions */}\n      <Box sx={{ mb: 4 }}>\n        <Typography\n          variant=\"h5\"\n          sx={{\n            fontWeight: 600,\n            mb: 3,\n            color: '#1e293b',\n            fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, sans-serif'\n          }}\n        >\n          Quick Actions\n        </Typography>\n        <Grid container spacing={3}>\n            {isAdmin ? (\n              <>\n                <Grid item xs={12} sm={6} md={3}>\n                  <QuickActionCard\n                    title=\"Manage Employees\"\n                    description=\"Add, edit, or remove employee records\"\n                    icon={<PeopleIcon />}\n                    color=\"#1e40af\"\n                    action={() => window.location.href = '/employees'}\n                  />\n                </Grid>\n                <Grid item xs={12} sm={6} md={3}>\n                  <QuickActionCard\n                    title=\"Review Documents\"\n                    description=\"Verify and approve employee documents\"\n                    icon={<DocumentIcon />}\n                    color=\"#059669\"\n                    action={() => window.location.href = '/documents'}\n                  />\n                </Grid>\n                <Grid item xs={12} sm={6} md={3}>\n                  <QuickActionCard\n                    title=\"Location Tracking\"\n                    description=\"Monitor employee locations and check-ins\"\n                    icon={<LocationIcon />}\n                    color=\"#d97706\"\n                    action={() => window.location.href = '/locations'}\n                  />\n                </Grid>\n              </>\n            ) : (\n              <>\n                <Grid item xs={12} sm={6} md={4}>\n                  <QuickActionCard\n                    title=\"Upload Documents\"\n                    description=\"Submit your documents for verification\"\n                    icon={<DocumentIcon />}\n                    color=\"#059669\"\n                    action={() => window.location.href = '/documents'}\n                  />\n                </Grid>\n                <Grid item xs={12} sm={6} md={4}>\n                  <QuickActionCard\n                    title={checkInStatus.isCheckedIn ? \"Check Out\" : \"Check In\"}\n                    description={checkInStatus.isCheckedIn ? \"End your work session\" : \"Start your work session\"}\n                    icon={<LocationIcon />}\n                    color={checkInStatus.isCheckedIn ? \"#dc2626\" : \"#34d399\"}\n                    action={() => window.location.href = '/locations'}\n                  />\n                </Grid>\n                <Grid item xs={12} sm={6} md={4}>\n                  <QuickActionCard\n                    title=\"My Profile\"\n                    description=\"View and update your profile information\"\n                    icon={<BusinessIcon />}\n                    color=\"#1e40af\"\n                    action={() => window.location.href = '/profile'}\n                  />\n                </Grid>\n              </>\n            )}\n        </Grid>\n      </Box>\n\n\n    </Box>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,SACEA,QAAQ,IAAIC,YAAY,EACxBC,WAAW,IAAIC,eAAe,EAC9BC,WAAW,IAAIC,YAAY,EAC3BC,UAAU,IAAIC,YAAY,EAC1BC,OAAO,IAAIC,WAAW,EACtBC,MAAM,IAAIC,UAAU,QACf,qBAAqB;AAC5B,SACEC,MAAM,EACNC,GAAG,EACHC,IAAI,EACJC,WAAW,EACXC,IAAI,EACJC,UAAU,QACL,eAAe;AACtB,SAASC,WAAW,EAAEC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AACpE,SAASC,WAAW,QAAQ,wBAAwB;AACpD,SAASC,eAAe,EAAEC,eAAe,EAAEC,eAAe,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpF,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM;IAAEC,IAAI;IAAEC;EAAQ,CAAC,GAAGd,UAAU,CAACG,WAAW,CAAC;EACjD,MAAM,CAACY,KAAK,EAAEC,QAAQ,CAAC,GAAGd,QAAQ,CAAC;IACjCe,cAAc,EAAE,CAAC;IACjBC,cAAc,EAAE,CAAC;IACjBC,gBAAgB,EAAE,CAAC;IACnBC,iBAAiB,EAAE,CAAC;IACpBC,iBAAiB,EAAE,CAAC;IACpBC,cAAc,EAAE;EAClB,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACuB,aAAa,EAAEC,gBAAgB,CAAC,GAAGxB,QAAQ,CAAC;IACjDyB,WAAW,EAAE,KAAK;IAClBC,WAAW,EAAE,IAAI;IACjBC,eAAe,EAAE;EACnB,CAAC,CAAC;EAEF,MAAMC,kBAAkB,GAAG/B,WAAW,CAAC,YAAY;IACjD,IAAI;MACFyB,UAAU,CAAC,IAAI,CAAC;MAEhB,IAAIV,OAAO,EAAE;QACX;QACA,MAAM,CAACiB,YAAY,EAAEC,YAAY,EAAEC,YAAY,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACnE9B,eAAe,CAAC+B,eAAe,CAAC,CAAC,EACjChC,eAAe,CAACiC,eAAe,CAAC,CAAC,EACjC/B,eAAe,CAACgC,eAAe,CAAC,CAAC,CAClC,CAAC;QAEF,MAAMC,SAAS,GAAGP,YAAY,CAACQ,IAAI,CAACA,IAAI,IAAI,EAAE;QAC9C,MAAMC,WAAW,GAAGF,SAAS,CAACG,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACC,kBAAkB,KAAK,SAAS,CAAC,CAACC,MAAM;QACxF,MAAMC,YAAY,GAAGP,SAAS,CAACG,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACC,kBAAkB,KAAK,UAAU,CAAC,CAACC,MAAM;QAC1F,MAAME,YAAY,GAAGR,SAAS,CAACG,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACC,kBAAkB,KAAK,UAAU,CAAC,CAACC,MAAM;QAE1F7B,QAAQ,CAAC;UACPC,cAAc,EAAEc,YAAY,CAACS,IAAI,CAACQ,KAAK,IAAI,CAAC;UAC5C9B,cAAc,EAAEqB,SAAS,CAACM,MAAM;UAChC1B,gBAAgB,EAAEsB,WAAW;UAC7BrB,iBAAiB,EAAE0B,YAAY;UAC/BzB,iBAAiB,EAAE0B,YAAY;UAC/BzB,cAAc,EAAEW,YAAY,CAACO,IAAI,CAACQ,KAAK,IAAI;QAC7C,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA,MAAM,CAAChB,YAAY,EAAEC,YAAY,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACrD/B,eAAe,CAACiC,eAAe,CAAC,CAAC,EACjC/B,eAAe,CAACgC,eAAe,CAAC,CAAC,CAClC,CAAC;QAEF,MAAMC,SAAS,GAAGP,YAAY,CAACQ,IAAI,CAACA,IAAI,IAAI,EAAE;QAC9C,MAAMC,WAAW,GAAGF,SAAS,CAACG,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACC,kBAAkB,KAAK,SAAS,CAAC,CAACC,MAAM;QACxF,MAAMC,YAAY,GAAGP,SAAS,CAACG,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACC,kBAAkB,KAAK,UAAU,CAAC,CAACC,MAAM;QAC1F,MAAME,YAAY,GAAGR,SAAS,CAACG,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACC,kBAAkB,KAAK,UAAU,CAAC,CAACC,MAAM;;QAE1F;QACA,MAAMI,SAAS,GAAGhB,YAAY,CAACO,IAAI,CAACA,IAAI,IAAI,EAAE;QAC9CU,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEtC,IAAI,CAACuC,EAAE,CAAC;QAC/DF,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEF,SAAS,CAAC;QAEnD,MAAMI,cAAc,GAAGJ,SAAS,CAACK,IAAI,CAACC,GAAG,IAAI;UAC3C,MAAMC,OAAO,GAAGD,GAAG,CAACE,QAAQ,KACZF,GAAG,CAACE,QAAQ,CAACC,GAAG,KAAK7C,IAAI,CAACuC,EAAE,IAAIG,GAAG,CAACE,QAAQ,CAAC5C,IAAI,KAAKA,IAAI,CAACuC,EAAE,CAAC,IAC/DG,GAAG,CAACI,MAAM,KAAK,YAAY;UAC1CT,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEI,GAAG,EAAE,QAAQ,EAAEC,OAAO,CAAC;UACpE,OAAOA,OAAO;QAChB,CAAC,CAAC;QAEF9B,gBAAgB,CAAC;UACfC,WAAW,EAAE,CAAC,CAAC0B,cAAc;UAC7BzB,WAAW,EAAE,CAAAyB,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEzB,WAAW,KAAI,IAAI;UAChDC,eAAe,EAAE,CAAAwB,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEO,OAAO,KAAI;QAC9C,CAAC,CAAC;QAEF5C,QAAQ,CAAC;UACPC,cAAc,EAAE,CAAC;UACjBC,cAAc,EAAEqB,SAAS,CAACM,MAAM;UAChC1B,gBAAgB,EAAEsB,WAAW;UAC7BrB,iBAAiB,EAAE0B,YAAY;UAC/BzB,iBAAiB,EAAE0B,YAAY;UAC/BzB,cAAc,EAAEW,YAAY,CAACO,IAAI,CAACQ,KAAK,IAAI;QAC7C,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOa,KAAK,EAAE;MACdX,OAAO,CAACW,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IACxD,CAAC,SAAS;MACRrC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACV,OAAO,EAAED,IAAI,CAAC,CAAC;EAEnBZ,SAAS,CAAC,MAAM;IACd6B,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,CAACA,kBAAkB,CAAC,CAAC;;EAExB;EACA7B,SAAS,CAAC,MAAM;IACd,MAAM6D,0BAA0B,GAAGA,CAAA,KAAM;MACvCZ,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;MAC/DrB,kBAAkB,CAAC,CAAC;IACtB,CAAC;;IAED;IACAiC,MAAM,CAACC,gBAAgB,CAAC,uBAAuB,EAAEF,0BAA0B,CAAC;;IAE5E;IACA,MAAMG,mBAAmB,GAAIC,CAAC,IAAK;MACjC,IAAIA,CAAC,CAACC,GAAG,KAAK,uBAAuB,EAAE;QACrCL,0BAA0B,CAAC,CAAC;MAC9B;IACF,CAAC;IACDC,MAAM,CAACC,gBAAgB,CAAC,SAAS,EAAEC,mBAAmB,CAAC;IAEvD,OAAO,MAAM;MACXF,MAAM,CAACK,mBAAmB,CAAC,uBAAuB,EAAEN,0BAA0B,CAAC;MAC/EC,MAAM,CAACK,mBAAmB,CAAC,SAAS,EAAEH,mBAAmB,CAAC;IAC5D,CAAC;EACH,CAAC,EAAE,CAACnC,kBAAkB,CAAC,CAAC;;EAExB;EACA7B,SAAS,CAAC,MAAM;IACd,IAAI,CAACa,OAAO,EAAE;MACZ,MAAMuD,YAAY,GAAGC,WAAW,CAAC,MAAM;QACrCpB,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;QACvDrB,kBAAkB,CAAC,CAAC;MACtB,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;MAEX,OAAO,MAAMyC,aAAa,CAACF,YAAY,CAAC;IAC1C;EACF,CAAC,EAAE,CAACvD,OAAO,EAAEgB,kBAAkB,CAAC,CAAC;EAEjC,MAAM0C,QAAQ,GAAGA,CAAC;IAAEC,KAAK;IAAEC,KAAK;IAAEC,IAAI;IAAEC,KAAK;IAAEC;EAAS,CAAC,kBACvDrE,OAAA,CAACb,IAAI;IACHmF,EAAE,EAAE;MACFC,MAAM,EAAE,MAAM;MACdC,eAAe,EAAE,SAAS;MAC1BC,MAAM,EAAE,mBAAmB;MAC3BC,YAAY,EAAE,CAAC;MACfC,UAAU,EAAE,sBAAsB;MAClC,SAAS,EAAE;QACTC,SAAS,EAAE,kBAAkB;QAC7BC,SAAS,EAAE,4BAA4B;QACvCC,WAAW,EAAEV;MACf;IACF,CAAE;IAAAW,QAAA,eAEF/E,OAAA,CAACZ,WAAW;MAACkF,EAAE,EAAE;QAAEU,CAAC,EAAE;MAAE,CAAE;MAAAD,QAAA,gBACxB/E,OAAA,CAACd,GAAG;QAACoF,EAAE,EAAE;UAAEW,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,gBACxD/E,OAAA,CAACf,MAAM;UACLqF,EAAE,EAAE;YACFc,OAAO,EAAEhB,KAAK;YACdiB,KAAK,EAAE,EAAE;YACTd,MAAM,EAAE,EAAE;YACVe,EAAE,EAAE;UACN,CAAE;UAAAP,QAAA,EAEDZ;QAAI;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACT1F,OAAA,CAACd,GAAG;UAACoF,EAAE,EAAE;YAAEqB,IAAI,EAAE;UAAE,CAAE;UAAAZ,QAAA,gBACnB/E,OAAA,CAACV,UAAU;YAACsG,OAAO,EAAC,OAAO;YAACtB,EAAE,EAAE;cAAEF,KAAK,EAAE,SAAS;cAAEyB,UAAU,EAAE,GAAG;cAAEV,EAAE,EAAE;YAAI,CAAE;YAAAJ,QAAA,EAC5Ed;UAAK;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eACb1F,OAAA,CAACV,UAAU;YAACsG,OAAO,EAAC,IAAI;YAACtB,EAAE,EAAE;cAAEuB,UAAU,EAAE,GAAG;cAAEzB,KAAK,EAAE,SAAS;cAAE0B,QAAQ,EAAE;YAAU,CAAE;YAAAf,QAAA,EACrFhE,OAAO,GAAG,KAAK,GAAGmD;UAAK;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACN1F,OAAA,CAACV,UAAU;QAACsG,OAAO,EAAC,SAAS;QAACtB,EAAE,EAAE;UAAEF,KAAK,EAAE;QAAU,CAAE;QAAAW,QAAA,EACpDV;MAAQ;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CACP;EAED,MAAMK,eAAe,GAAGA,CAAC;IAAE9B,KAAK;IAAE+B,WAAW;IAAE7B,IAAI;IAAEC,KAAK;IAAE6B;EAAO,CAAC,kBAClEjG,OAAA,CAACb,IAAI;IACHmF,EAAE,EAAE;MACFC,MAAM,EAAE,MAAM;MACd2B,MAAM,EAAE,SAAS;MACjB1B,eAAe,EAAE,SAAS;MAC1BC,MAAM,EAAE,mBAAmB;MAC3BC,YAAY,EAAE,CAAC;MACfC,UAAU,EAAE,sBAAsB;MAClC,SAAS,EAAE;QACTC,SAAS,EAAE,kBAAkB;QAC7BC,SAAS,EAAE,4BAA4B;QACvCC,WAAW,EAAEV;MACf;IACF,CAAE;IACF+B,OAAO,EAAEF,MAAO;IAAAlB,QAAA,eAEhB/E,OAAA,CAACZ,WAAW;MAACkF,EAAE,EAAE;QAAEU,CAAC,EAAE,CAAC;QAAEoB,SAAS,EAAE;MAAS,CAAE;MAAArB,QAAA,gBAC7C/E,OAAA,CAACf,MAAM;QACLqF,EAAE,EAAE;UACFc,OAAO,EAAEhB,KAAK;UACdA,KAAK,EAAE,SAAS;UAChBiB,KAAK,EAAE,EAAE;UACTd,MAAM,EAAE,EAAE;UACV8B,EAAE,EAAE,MAAM;UACVlB,EAAE,EAAE;QACN,CAAE;QAAAJ,QAAA,EAEDZ;MAAI;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACT1F,OAAA,CAACV,UAAU;QAACsG,OAAO,EAAC,IAAI;QAACtB,EAAE,EAAE;UAAEuB,UAAU,EAAE,GAAG;UAAEV,EAAE,EAAE,CAAC;UAAEf,KAAK,EAAE;QAAU,CAAE;QAAAW,QAAA,EACvEd;MAAK;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eACb1F,OAAA,CAACV,UAAU;QAACsG,OAAO,EAAC,OAAO;QAACtB,EAAE,EAAE;UAAEF,KAAK,EAAE;QAAU,CAAE;QAAAW,QAAA,EAClDiB;MAAW;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CACP;EAED,oBACE1F,OAAA,CAACd,GAAG;IAACoF,EAAE,EAAE;MAAEe,KAAK,EAAE;IAAO,CAAE;IAAAN,QAAA,gBAEzB/E,OAAA,CAACd,GAAG;MAACoF,EAAE,EAAE;QAAEa,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACjB/E,OAAA,CAACV,UAAU;QACTsG,OAAO,EAAE;UAAEU,EAAE,EAAE,IAAI;UAAEC,EAAE,EAAE;QAAK,CAAE;QAChCjC,EAAE,EAAE;UACFuB,UAAU,EAAE,GAAG;UACfzB,KAAK,EAAE,SAAS;UAChBe,EAAE,EAAE,CAAC;UACLqB,UAAU,EAAE;QACd,CAAE;QAAAzB,QAAA,GACH,gBACe,EAAC1E,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoG,IAAI,EAAC,gBAC5B;MAAA;QAAAlB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb1F,OAAA,CAACV,UAAU;QACTsG,OAAO,EAAC,OAAO;QACftB,EAAE,EAAE;UACFF,KAAK,EAAE,SAAS;UAChBoC,UAAU,EAAE;QACd,CAAE;QAAAzB,QAAA,EAEDzE,OAAO,GAAG,iDAAiD,GAAG;MAA2C;QAAAiF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAEN1F,OAAA,CAACX,IAAI;MAACqH,SAAS;MAACC,OAAO,EAAE,CAAE;MAACrC,EAAE,EAAE;QAAEa,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,GACrCzE,OAAO,iBACNN,OAAA,CAACX,IAAI;QAACuH,IAAI;QAACN,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACM,EAAE,EAAE,CAAE;QAAA9B,QAAA,eAC9B/E,OAAA,CAACgE,QAAQ;UACPC,KAAK,EAAC,iBAAiB;UACvBC,KAAK,EAAE3D,KAAK,CAACE,cAAe;UAC5B0D,IAAI,eAAEnE,OAAA,CAAChB,UAAU;YAAAuG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACrBtB,KAAK,EAAC,SAAS;UACfC,QAAQ,EAAC;QAAsB;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACP,eACD1F,OAAA,CAACX,IAAI;QAACuH,IAAI;QAACN,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACM,EAAE,EAAE,CAAE;QAAA9B,QAAA,eAC9B/E,OAAA,CAACgE,QAAQ;UACPC,KAAK,EAAC,iBAAiB;UACvBC,KAAK,EAAE3D,KAAK,CAACG,cAAe;UAC5ByD,IAAI,eAAEnE,OAAA,CAACtB,YAAY;YAAA6G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBtB,KAAK,EAAC,SAAS;UACfC,QAAQ,EAAE/D,OAAO,GAAG,eAAe,GAAG;QAAe;UAAAiF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACP1F,OAAA,CAACX,IAAI;QAACuH,IAAI;QAACN,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACM,EAAE,EAAE,CAAE;QAAA9B,QAAA,eAC9B/E,OAAA,CAACgE,QAAQ;UACPC,KAAK,EAAC,gBAAgB;UACtBC,KAAK,EAAE3D,KAAK,CAACI,gBAAiB;UAC9BwD,IAAI,eAAEnE,OAAA,CAAClB,WAAW;YAAAyG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACtBtB,KAAK,EAAC,SAAS;UACfC,QAAQ,EAAC;QAAmB;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACP1F,OAAA,CAACX,IAAI;QAACuH,IAAI;QAACN,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACM,EAAE,EAAE,CAAE;QAAA9B,QAAA,eAC9B/E,OAAA,CAACgE,QAAQ;UACPC,KAAK,EAAC,oBAAoB;UAC1BC,KAAK,EAAE3D,KAAK,CAACK,iBAAkB;UAC/BuD,IAAI,eAAEnE,OAAA,CAACxB,eAAe;YAAA+G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC1BtB,KAAK,EAAC,SAAS;UACfC,QAAQ,EAAC;QAAoB;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEH,CAAC,EAGR,CAACpF,OAAO,iBACPN,OAAA,CAACd,GAAG;MAACoF,EAAE,EAAE;QAAEa,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,eACjB/E,OAAA,CAACb,IAAI;QACHmF,EAAE,EAAE;UACFwC,UAAU,EAAE7F,aAAa,CAACE,WAAW,GACjC,mDAAmD,GACnD,mDAAmD;UACvDsD,MAAM,EAAExD,aAAa,CAACE,WAAW,GAC7B,mBAAmB,GACnB,mBAAmB;UACvBuD,YAAY,EAAE,CAAC;UACfC,UAAU,EAAE,sBAAsB;UAClC,SAAS,EAAE;YACTC,SAAS,EAAE,kBAAkB;YAC7BC,SAAS,EAAE5D,aAAa,CAACE,WAAW,GAChC,mCAAmC,GACnC;UACN;QACF,CAAE;QAAA4D,QAAA,eAEF/E,OAAA,CAACZ,WAAW;UAACkF,EAAE,EAAE;YAAEU,CAAC,EAAE;UAAE,CAAE;UAAAD,QAAA,eACxB/E,OAAA,CAACd,GAAG;YAACoF,EAAE,EAAE;cAAEW,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAE6B,cAAc,EAAE;YAAgB,CAAE;YAAAhC,QAAA,gBAClF/E,OAAA,CAACd,GAAG;cAACoF,EAAE,EAAE;gBAAEW,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE;cAAS,CAAE;cAAAH,QAAA,gBACjD/E,OAAA,CAACf,MAAM;gBACLqF,EAAE,EAAE;kBACFc,OAAO,EAAEnE,aAAa,CAACE,WAAW,GAAG,SAAS,GAAG,SAAS;kBAC1DkE,KAAK,EAAE,EAAE;kBACTd,MAAM,EAAE,EAAE;kBACVe,EAAE,EAAE,CAAC;kBACLT,SAAS,EAAE;gBACb,CAAE;gBAAAE,QAAA,eAEF/E,OAAA,CAACpB,YAAY;kBAAC0F,EAAE,EAAE;oBAAEwB,QAAQ,EAAE;kBAAG;gBAAE;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC,eACT1F,OAAA,CAACd,GAAG;gBAAA6F,QAAA,gBACF/E,OAAA,CAACV,UAAU;kBACTsG,OAAO,EAAC,IAAI;kBACZtB,EAAE,EAAE;oBACFuB,UAAU,EAAE,GAAG;oBACfzB,KAAK,EAAEnD,aAAa,CAACE,WAAW,GAAG,SAAS,GAAG,SAAS;oBACxDgE,EAAE,EAAE,GAAG;oBACPqB,UAAU,EAAE;kBACd,CAAE;kBAAAzB,QAAA,EAED9D,aAAa,CAACE,WAAW,GAAG,cAAc,GAAG;gBAAkB;kBAAAoE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC,eACb1F,OAAA,CAACV,UAAU;kBACTsG,OAAO,EAAC,OAAO;kBACftB,EAAE,EAAE;oBACFF,KAAK,EAAEnD,aAAa,CAACE,WAAW,GAAG,SAAS,GAAG,SAAS;oBACxD0E,UAAU,EAAE;kBACd,CAAE;kBAAAd,QAAA,EAED9D,aAAa,CAACE,WAAW,GACtB,UAAU,IAAI6F,IAAI,CAAC/F,aAAa,CAACG,WAAW,CAAC,CAAC6F,cAAc,CAAC,CAAC,EAAE,GAChE;gBAA+B;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEzB,CAAC,EACZzE,aAAa,CAACE,WAAW,IAAIF,aAAa,CAACI,eAAe,iBACzDrB,OAAA,CAACV,UAAU;kBACTsG,OAAO,EAAC,SAAS;kBACjBtB,EAAE,EAAE;oBACFF,KAAK,EAAE,SAAS;oBAChByB,UAAU,EAAE,GAAG;oBACfZ,OAAO,EAAE,OAAO;oBAChBiC,EAAE,EAAE;kBACN,CAAE;kBAAAnC,QAAA,GACH,eACI,EAAC9D,aAAa,CAACI,eAAe,CAACgB,MAAM,GAAG,EAAE,GACzCpB,aAAa,CAACI,eAAe,CAAC8F,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GACtDlG,aAAa,CAACI,eAAe;gBAAA;kBAAAkE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEvB,CACb;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN1F,OAAA,CAACd,GAAG;cAACoF,EAAE,EAAE;gBAAE8B,SAAS,EAAE;cAAS,CAAE;cAAArB,QAAA,gBAC/B/E,OAAA,CAACV,UAAU;gBACTsG,OAAO,EAAC,SAAS;gBACjBtB,EAAE,EAAE;kBACFF,KAAK,EAAEnD,aAAa,CAACE,WAAW,GAAG,SAAS,GAAG,SAAS;kBACxD0E,UAAU,EAAE,GAAG;kBACfuB,aAAa,EAAE,WAAW;kBAC1BC,aAAa,EAAE;gBACjB,CAAE;gBAAAtC,QAAA,EACH;cAED;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb1F,OAAA,CAACV,UAAU;gBACTsG,OAAO,EAAC,IAAI;gBACZtB,EAAE,EAAE;kBACFuB,UAAU,EAAE,GAAG;kBACfzB,KAAK,EAAEnD,aAAa,CAACE,WAAW,GAAG,SAAS,GAAG,SAAS;kBACxDmG,UAAU,EAAE;gBACd,CAAE;gBAAAvC,QAAA,EAED9D,aAAa,CAACE,WAAW,GAAG,IAAI,GAAG;cAAI;gBAAAoE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CACN,eAGD1F,OAAA,CAACd,GAAG;MAACoF,EAAE,EAAE;QAAEa,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACjB/E,OAAA,CAACV,UAAU;QACTsG,OAAO,EAAC,IAAI;QACZtB,EAAE,EAAE;UACFuB,UAAU,EAAE,GAAG;UACfV,EAAE,EAAE,CAAC;UACLf,KAAK,EAAE,SAAS;UAChBoC,UAAU,EAAE;QACd,CAAE;QAAAzB,QAAA,EACH;MAED;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb1F,OAAA,CAACX,IAAI;QAACqH,SAAS;QAACC,OAAO,EAAE,CAAE;QAAA5B,QAAA,EACtBzE,OAAO,gBACNN,OAAA,CAAAE,SAAA;UAAA6E,QAAA,gBACE/E,OAAA,CAACX,IAAI;YAACuH,IAAI;YAACN,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACM,EAAE,EAAE,CAAE;YAAA9B,QAAA,eAC9B/E,OAAA,CAAC+F,eAAe;cACd9B,KAAK,EAAC,kBAAkB;cACxB+B,WAAW,EAAC,uCAAuC;cACnD7B,IAAI,eAAEnE,OAAA,CAAChB,UAAU;gBAAAuG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACrBtB,KAAK,EAAC,SAAS;cACf6B,MAAM,EAAEA,CAAA,KAAM1C,MAAM,CAACgE,QAAQ,CAACC,IAAI,GAAG;YAAa;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACP1F,OAAA,CAACX,IAAI;YAACuH,IAAI;YAACN,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACM,EAAE,EAAE,CAAE;YAAA9B,QAAA,eAC9B/E,OAAA,CAAC+F,eAAe;cACd9B,KAAK,EAAC,kBAAkB;cACxB+B,WAAW,EAAC,uCAAuC;cACnD7B,IAAI,eAAEnE,OAAA,CAACtB,YAAY;gBAAA6G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvBtB,KAAK,EAAC,SAAS;cACf6B,MAAM,EAAEA,CAAA,KAAM1C,MAAM,CAACgE,QAAQ,CAACC,IAAI,GAAG;YAAa;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACP1F,OAAA,CAACX,IAAI;YAACuH,IAAI;YAACN,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACM,EAAE,EAAE,CAAE;YAAA9B,QAAA,eAC9B/E,OAAA,CAAC+F,eAAe;cACd9B,KAAK,EAAC,mBAAmB;cACzB+B,WAAW,EAAC,0CAA0C;cACtD7B,IAAI,eAAEnE,OAAA,CAACpB,YAAY;gBAAA2G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvBtB,KAAK,EAAC,SAAS;cACf6B,MAAM,EAAEA,CAAA,KAAM1C,MAAM,CAACgE,QAAQ,CAACC,IAAI,GAAG;YAAa;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA,eACP,CAAC,gBAEH1F,OAAA,CAAAE,SAAA;UAAA6E,QAAA,gBACE/E,OAAA,CAACX,IAAI;YAACuH,IAAI;YAACN,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACM,EAAE,EAAE,CAAE;YAAA9B,QAAA,eAC9B/E,OAAA,CAAC+F,eAAe;cACd9B,KAAK,EAAC,kBAAkB;cACxB+B,WAAW,EAAC,wCAAwC;cACpD7B,IAAI,eAAEnE,OAAA,CAACtB,YAAY;gBAAA6G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvBtB,KAAK,EAAC,SAAS;cACf6B,MAAM,EAAEA,CAAA,KAAM1C,MAAM,CAACgE,QAAQ,CAACC,IAAI,GAAG;YAAa;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACP1F,OAAA,CAACX,IAAI;YAACuH,IAAI;YAACN,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACM,EAAE,EAAE,CAAE;YAAA9B,QAAA,eAC9B/E,OAAA,CAAC+F,eAAe;cACd9B,KAAK,EAAEhD,aAAa,CAACE,WAAW,GAAG,WAAW,GAAG,UAAW;cAC5D6E,WAAW,EAAE/E,aAAa,CAACE,WAAW,GAAG,uBAAuB,GAAG,yBAA0B;cAC7FgD,IAAI,eAAEnE,OAAA,CAACpB,YAAY;gBAAA2G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvBtB,KAAK,EAAEnD,aAAa,CAACE,WAAW,GAAG,SAAS,GAAG,SAAU;cACzD8E,MAAM,EAAEA,CAAA,KAAM1C,MAAM,CAACgE,QAAQ,CAACC,IAAI,GAAG;YAAa;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACP1F,OAAA,CAACX,IAAI;YAACuH,IAAI;YAACN,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACM,EAAE,EAAE,CAAE;YAAA9B,QAAA,eAC9B/E,OAAA,CAAC+F,eAAe;cACd9B,KAAK,EAAC,YAAY;cAClB+B,WAAW,EAAC,0CAA0C;cACtD7B,IAAI,eAAEnE,OAAA,CAAC1B,YAAY;gBAAAiH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvBtB,KAAK,EAAC,SAAS;cACf6B,MAAM,EAAEA,CAAA,KAAM1C,MAAM,CAACgE,QAAQ,CAACC,IAAI,GAAG;YAAW;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA,eACP;MACH;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAGH,CAAC;AAEV,CAAC;AAACtF,EAAA,CAndID,SAAS;AAAAsH,EAAA,GAATtH,SAAS;AAqdf,eAAeA,SAAS;AAAC,IAAAsH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}