{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\EmployeeManagementSystem\\\\frontend\\\\src\\\\pages\\\\Locations.js\",\n  _s = $RefreshSig$();\nimport { Logout as CheckOutIcon, Delete as DeleteIcon, MyLocation as LocationIcon } from '@mui/icons-material';\nimport { Box, Button, Card, CardContent, Chip, CircularProgress, Container, Grid, IconButton, MenuItem, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, TextField, Typography } from '@mui/material';\nimport { useContext, useEffect, useState } from 'react';\nimport { toast } from 'react-toastify';\nimport { AuthContext } from '../context/AuthContext';\nimport { employeeService, locationService } from '../services/api';\n\n// No mock data - fetch from database\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Locations = () => {\n  _s();\n  const {\n    isAdmin\n  } = useContext(AuthContext);\n  const [locations, setLocations] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [locationError, setLocationError] = useState(null);\n  const [isCheckedIn, setIsCheckedIn] = useState(false);\n  const [currentLocationId, setCurrentLocationId] = useState(null);\n\n  // Filtering and Sorting State (Admin only)\n  const [filters, setFilters] = useState({\n    status: 'all',\n    employee: 'all'\n  });\n  const [sortBy, setSortBy] = useState('checkInTime');\n  const [sortOrder, setSortOrder] = useState('desc');\n  const [employees, setEmployees] = useState([]);\n\n  // Fetch locations and employees function\n  const fetchLocations = async () => {\n    try {\n      const response = await locationService.getAllLocations();\n      if (response.data.success) {\n        const formattedLocations = response.data.data.map(loc => {\n          var _loc$employee, _loc$employee$user, _loc$employee2;\n          return {\n            id: loc._id,\n            latitude: loc.latitude,\n            // Keep for Google Maps but don't display\n            longitude: loc.longitude,\n            // Keep for Google Maps but don't display\n            address: loc.address,\n            checkInTime: new Date(loc.checkInTime).toLocaleString(),\n            checkOutTime: loc.checkOutTime ? new Date(loc.checkOutTime).toLocaleString() : null,\n            status: loc.status,\n            employeeName: ((_loc$employee = loc.employee) === null || _loc$employee === void 0 ? void 0 : (_loc$employee$user = _loc$employee.user) === null || _loc$employee$user === void 0 ? void 0 : _loc$employee$user.name) || 'Unknown',\n            employeeId: ((_loc$employee2 = loc.employee) === null || _loc$employee2 === void 0 ? void 0 : _loc$employee2.employeeId) || 'N/A'\n          };\n        });\n        setLocations(formattedLocations);\n\n        // Check if current user (employee) is checked in\n        if (!isAdmin) {\n          const currentUserCheckedIn = formattedLocations.find(loc => loc.status === 'checked-in');\n          if (currentUserCheckedIn) {\n            setIsCheckedIn(true);\n            setCurrentLocationId(currentUserCheckedIn.id);\n          } else {\n            setIsCheckedIn(false);\n            setCurrentLocationId(null);\n          }\n        }\n      }\n\n      // Fetch employees for admin dropdown (only if admin)\n      if (isAdmin) {\n        try {\n          const employeesResponse = await employeeService.getAllEmployees();\n          if (employeesResponse.data.success) {\n            const formattedEmployees = employeesResponse.data.data.map(emp => {\n              var _emp$user;\n              return {\n                id: emp._id,\n                name: ((_emp$user = emp.user) === null || _emp$user === void 0 ? void 0 : _emp$user.name) || 'Unknown',\n                employeeId: emp.employeeId || 'N/A'\n              };\n            });\n            setEmployees(formattedEmployees);\n          }\n        } catch (empError) {\n          console.error('Error fetching employees:', empError);\n          // Don't show error toast for employees as it's not critical\n        }\n      }\n    } catch (error) {\n      console.error('Error fetching locations:', error);\n      toast.error('Failed to fetch locations');\n    }\n  };\n  useEffect(() => {\n    fetchLocations();\n  }, [isAdmin]);\n  const handleInstantCheckIn = () => {\n    setLocationError(null);\n    setLoading(true);\n\n    // Get current location with high accuracy and check in immediately\n    if (navigator.geolocation) {\n      navigator.geolocation.getCurrentPosition(async position => {\n        const location = {\n          latitude: position.coords.latitude,\n          longitude: position.coords.longitude,\n          accuracy: position.coords.accuracy\n        };\n\n        // Get address from coordinates using reverse geocoding\n        let address;\n        try {\n          // Using a simple reverse geocoding service\n          const response = await fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${location.latitude}&lon=${location.longitude}`);\n          const data = await response.json();\n          if (data && data.display_name) {\n            address = data.display_name;\n          } else {\n            address = `Location: ${location.latitude.toFixed(4)}, ${location.longitude.toFixed(4)}`;\n          }\n        } catch (error) {\n          // Fallback to coordinates if geocoding fails\n          address = `Location: ${location.latitude.toFixed(4)}, ${location.longitude.toFixed(4)}`;\n        }\n\n        // Immediately check in without confirmation\n        await handleCheckIn(location, address);\n      }, error => {\n        let errorMessage = 'Unable to get your location. ';\n        switch (error.code) {\n          case error.PERMISSION_DENIED:\n            errorMessage += 'Location access denied. Please enable location permissions.';\n            break;\n          case error.POSITION_UNAVAILABLE:\n            errorMessage += 'Location information unavailable.';\n            break;\n          case error.TIMEOUT:\n            errorMessage += 'Location request timed out.';\n            break;\n          default:\n            errorMessage += 'An unknown error occurred.';\n            break;\n        }\n        setLocationError(errorMessage);\n        setLoading(false);\n        toast.error(errorMessage);\n      }, {\n        enableHighAccuracy: true,\n        timeout: 10000,\n        maximumAge: 0\n      });\n    } else {\n      const errorMessage = 'Geolocation is not supported by this browser. Please use a modern browser.';\n      setLocationError(errorMessage);\n      setLoading(false);\n      toast.error(errorMessage);\n    }\n  };\n  const handleCheckIn = async (location, addressText) => {\n    if (!location) {\n      toast.error('Location not available. Please try again.');\n      setLoading(false);\n      return;\n    }\n    try {\n      // Get device and IP information\n      const deviceInfo = {\n        userAgent: navigator.userAgent,\n        platform: navigator.platform,\n        language: navigator.language\n      };\n      const locationData = {\n        latitude: location.latitude,\n        longitude: location.longitude,\n        address: addressText,\n        device: `${deviceInfo.platform} - ${deviceInfo.userAgent.split(' ')[0]}`,\n        accuracy: location.accuracy\n      };\n      console.log('Checking in with location data:', locationData);\n      const response = await locationService.checkIn(locationData);\n      if (response.data.success) {\n        // Update state immediately\n        setIsCheckedIn(true);\n        setCurrentLocationId(response.data.data._id);\n\n        // Refresh locations list\n        await fetchLocations();\n\n        // Show single success notification\n        toast.success(' Checked in successfully!');\n\n        // Start continuous location tracking\n        startLiveLocationTracking(response.data.data._id);\n\n        // Notify other components (like Dashboard) about location status change\n        window.dispatchEvent(new CustomEvent('locationStatusChanged'));\n        localStorage.setItem('locationStatusChanged', Date.now().toString());\n      } else {\n        throw new Error('Check-in failed');\n      }\n    } catch (error) {\n      console.error('Check-in error:', error);\n      if (error.response) {\n        toast.error(`Check-in failed: ${error.response.data.error || 'Server error'}`);\n      } else if (error.request) {\n        toast.error('No response from server. Please check if backend is running.');\n      } else {\n        toast.error('Failed to check in. Please try again.');\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleCheckOut = async id => {\n    try {\n      setLoading(true);\n      const response = await locationService.checkOut(id);\n      if (response.data.success) {\n        // Update state immediately\n        setIsCheckedIn(false);\n        setCurrentLocationId(null);\n\n        // Refresh locations list\n        await fetchLocations();\n\n        // Show single success notification\n        toast.success('Successfully checked out! ');\n\n        // Stop live location tracking\n        stopLiveLocationTracking();\n      } else {\n        throw new Error('Check-out failed');\n      }\n    } catch (error) {\n      console.error('Check-out error:', error);\n      if (error.response) {\n        toast.error(`Check-out failed: ${error.response.data.error || 'Server error'}`);\n      } else if (error.request) {\n        toast.error('No response from server. Please check if backend is running.');\n      } else {\n        toast.error('Failed to check out. Please try again.');\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Live location tracking functions\n  const [watchId, setWatchId] = useState(null);\n  const [activeLocationId, setActiveLocationId] = useState(null);\n  const startLiveLocationTracking = locationId => {\n    if (navigator.geolocation && !watchId) {\n      setActiveLocationId(locationId);\n      const id = navigator.geolocation.watchPosition(async position => {\n        const newLocation = {\n          latitude: position.coords.latitude,\n          longitude: position.coords.longitude,\n          accuracy: position.coords.accuracy,\n          timestamp: new Date().toISOString()\n        };\n        console.log('Live location update:', newLocation);\n\n        // Send real-time location update to server\n        try {\n          await locationService.updateLiveLocation(locationId, newLocation);\n          console.log('Location updated on server');\n        } catch (error) {\n          console.error('Failed to update location on server:', error);\n        }\n      }, error => {\n        console.error('Live location tracking error:', error);\n        toast.error('Location tracking error. Please check your GPS settings.');\n      }, {\n        enableHighAccuracy: true,\n        timeout: 15000,\n        maximumAge: 30000 // 30 seconds\n      });\n      setWatchId(id);\n      console.log('🔴 Live location tracking started for location:', locationId);\n    }\n  };\n  const stopLiveLocationTracking = () => {\n    if (watchId) {\n      navigator.geolocation.clearWatch(watchId);\n      setWatchId(null);\n      setActiveLocationId(null);\n      console.log('🔴 Live location tracking stopped');\n    }\n  };\n\n  // Clean up location tracking on component unmount\n  useEffect(() => {\n    return () => {\n      stopLiveLocationTracking();\n    };\n  }, [watchId]);\n  const handleDeleteLocation = id => {\n    setLocations(locations.filter(loc => loc.id !== id));\n    toast.success('Location record deleted successfully!');\n  };\n\n  // Handle viewing live location on Google Maps\n  const handleViewLiveLocation = location => {\n    const {\n      latitude,\n      longitude,\n      employeeName,\n      employeeId\n    } = location;\n\n    // Create Google Maps URL with the employee's location (high zoom for precise location)\n    const googleMapsUrl = `https://www.google.com/maps?q=${latitude},${longitude}&z=19&t=h`;\n\n    // Open Google Maps in a new tab\n    window.open(googleMapsUrl, '_blank');\n\n    // Show success message with employee info\n    toast.success(`📍 Viewing ${employeeName}'s live location on Google Maps`, {\n      position: \"top-right\",\n      autoClose: 3000,\n      hideProgressBar: false,\n      closeOnClick: true,\n      pauseOnHover: true,\n      draggable: true\n    });\n    console.log('📍 Google Maps opened for employee:', {\n      name: employeeName,\n      id: employeeId,\n      coordinates: `${latitude}, ${longitude}`,\n      mapsUrl: googleMapsUrl,\n      timestamp: new Date().toISOString()\n    });\n  };\n  const getStatusChip = status => {\n    return status === 'checked-in' ? /*#__PURE__*/_jsxDEV(Chip, {\n      label: \"Checked In\",\n      size: \"small\",\n      sx: {\n        fontWeight: 600,\n        backgroundColor: '#dcfce7',\n        // Light green background\n        color: '#166534',\n        // Dark green text\n        border: '1px solid #bbf7d0',\n        // Light green border\n        '&:hover': {\n          backgroundColor: '#bbf7d0' // Slightly darker on hover\n        }\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 375,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(Chip, {\n      label: \"Checked Out\",\n      size: \"small\",\n      sx: {\n        fontWeight: 500,\n        backgroundColor: '#f1f5f9',\n        // Light gray background\n        color: '#64748b',\n        // Gray text\n        border: '1px solid #e2e8f0' // Light gray border\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 388,\n      columnNumber: 9\n    }, this);\n  };\n\n  // Filter and Sort Locations (Admin only)\n  const getFilteredAndSortedLocations = () => {\n    if (!isAdmin) return locations;\n    let filtered = locations.filter(loc => {\n      const statusMatch = filters.status === 'all' || loc.status === filters.status;\n      const employeeMatch = filters.employee === 'all' || loc.employeeName.toLowerCase().includes(filters.employee.toLowerCase());\n      return statusMatch && employeeMatch;\n    });\n\n    // Sort locations\n    filtered.sort((a, b) => {\n      let aValue = a[sortBy];\n      let bValue = b[sortBy];\n\n      // Handle date sorting\n      if (sortBy === 'checkInTime' || sortBy === 'checkOutTime') {\n        aValue = new Date(aValue || 0);\n        bValue = new Date(bValue || 0);\n      }\n\n      // Handle string sorting\n      if (typeof aValue === 'string') {\n        aValue = aValue.toLowerCase();\n        bValue = bValue.toLowerCase();\n      }\n      if (sortOrder === 'asc') {\n        return aValue > bValue ? 1 : -1;\n      } else {\n        return aValue < bValue ? 1 : -1;\n      }\n    });\n    return filtered;\n  };\n  const handleFilterChange = (filterType, value) => {\n    setFilters(prev => ({\n      ...prev,\n      [filterType]: value\n    }));\n  };\n  const filteredLocations = getFilteredAndSortedLocations();\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"lg\",\n    sx: {\n      mt: 4,\n      mb: 4\n    },\n    children: /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: {\n            xs: 'flex-start',\n            sm: 'center'\n          },\n          flexDirection: {\n            xs: 'column',\n            sm: 'row'\n          },\n          gap: {\n            xs: 2,\n            sm: 0\n          },\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          component: \"h1\",\n          children: \"Location Tracking\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 458,\n          columnNumber: 11\n        }, this), !isAdmin && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: !isCheckedIn ? /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20,\n              sx: {\n                color: '#ffffff'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 466,\n              columnNumber: 40\n            }, this) : /*#__PURE__*/_jsxDEV(LocationIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 466,\n              columnNumber: 99\n            }, this),\n            onClick: handleInstantCheckIn,\n            disabled: loading,\n            sx: {\n              background: 'linear-gradient(135deg, #34d399 0%, #10b981 100%)',\n              alignSelf: {\n                xs: 'stretch',\n                sm: 'auto'\n              },\n              '&:hover': {\n                background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',\n                transform: 'translateY(-1px)',\n                boxShadow: '0 4px 12px rgba(52, 211, 153, 0.3)'\n              },\n              '&:disabled': {\n                background: '#e5e7eb',\n                color: '#9ca3af'\n              },\n              transition: 'all 0.2s ease-in-out'\n            },\n            children: loading ? 'Checking In...' : '📍 Check In'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 464,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20,\n              sx: {\n                color: '#ffffff'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 489,\n              columnNumber: 40\n            }, this) : /*#__PURE__*/_jsxDEV(CheckOutIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 489,\n              columnNumber: 99\n            }, this),\n            onClick: () => handleCheckOut(currentLocationId),\n            disabled: loading,\n            sx: {\n              background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n              alignSelf: {\n                xs: 'stretch',\n                sm: 'auto'\n              },\n              '&:hover': {\n                background: 'linear-gradient(135deg, #dc2626 0%, #b91c1c 100%)',\n                transform: 'translateY(-1px)',\n                boxShadow: '0 4px 12px rgba(239, 68, 68, 0.3)'\n              },\n              '&:disabled': {\n                background: '#e5e7eb',\n                color: '#9ca3af'\n              },\n              transition: 'all 0.2s ease-in-out'\n            },\n            children: loading ? 'Checking Out...' : '🔴 Check Out'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 487,\n            columnNumber: 17\n          }, this)\n        }, void 0, false)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 450,\n        columnNumber: 9\n      }, this), locationError && /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 2,\n          mb: 3,\n          bgcolor: 'error.light'\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          color: \"error\",\n          children: locationError\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 516,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 515,\n        columnNumber: 11\n      }, this), !isAdmin && locations.some(loc => loc.status === 'checked-in') && /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          mb: 3,\n          bgcolor: 'success.light'\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"You are currently checked in\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 523,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: \"Don't forget to check out at the end of your work day.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 526,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 522,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 521,\n        columnNumber: 11\n      }, this), isAdmin && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 3,\n          p: 2,\n          bgcolor: '#f8fafc',\n          borderRadius: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            mb: 2,\n            fontWeight: 600\n          },\n          children: \"Filter & Sort Locations\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 536,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              select: true,\n              fullWidth: true,\n              label: \"Status\",\n              value: filters.status,\n              onChange: e => handleFilterChange('status', e.target.value),\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"all\",\n                children: \"All Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 549,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"checked-in\",\n                children: \"Checked In\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 550,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"checked-out\",\n                children: \"Checked Out\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 551,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 541,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 540,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              select: true,\n              fullWidth: true,\n              label: \"Filter by Employee\",\n              value: filters.employee,\n              onChange: e => handleFilterChange('employee', e.target.value),\n              size: \"small\",\n              sx: {\n                '& .MuiOutlinedInput-root': {\n                  borderRadius: 2,\n                  backgroundColor: '#ffffff',\n                  '&:hover fieldset': {\n                    borderColor: '#3b82f6'\n                  },\n                  '&.Mui-focused fieldset': {\n                    borderColor: '#1e40af',\n                    borderWidth: '2px'\n                  }\n                },\n                '& .MuiInputLabel-root': {\n                  '&.Mui-focused': {\n                    color: '#1e40af',\n                    fontWeight: 600\n                  }\n                }\n              },\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"all\",\n                children: \"All Employees\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 582,\n                columnNumber: 19\n              }, this), employees.map(employee => /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: employee.name,\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    flexDirection: 'column'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      fontWeight: 500\n                    },\n                    children: employee.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 586,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"text.secondary\",\n                    children: [\"ID: \", employee.employeeId]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 589,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 585,\n                  columnNumber: 23\n                }, this)\n              }, employee.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 584,\n                columnNumber: 21\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 555,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 554,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              select: true,\n              fullWidth: true,\n              label: \"Sort By\",\n              value: sortBy,\n              onChange: e => setSortBy(e.target.value),\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"checkInTime\",\n                children: \"Check In Time\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 606,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"checkOutTime\",\n                children: \"Check Out Time\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 607,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"employeeName\",\n                children: \"Employee Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 608,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"status\",\n                children: \"Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 609,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 598,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 597,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 539,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 2,\n            display: 'flex',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            size: \"small\",\n            onClick: () => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc'),\n            children: sortOrder === 'asc' ? '↑ Ascending' : '↓ Descending'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 614,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            size: \"small\",\n            onClick: () => {\n              setFilters({\n                status: 'all',\n                employee: 'all'\n              });\n              setSortBy('checkInTime');\n              setSortOrder('desc');\n            },\n            children: \"Clear Filters\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 621,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 613,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 535,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n        component: Paper,\n        variant: \"outlined\",\n        sx: {\n          overflowX: 'auto',\n          '& .MuiTable-root': {\n            minWidth: {\n              xs: 300,\n              sm: 650\n            }\n          }\n        },\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [isAdmin && /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Employee\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 649,\n                columnNumber: 29\n              }, this), isAdmin && /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Location\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 650,\n                columnNumber: 29\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Check In Time\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 651,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Check Out Time\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 652,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 653,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"right\",\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 654,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 648,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 647,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: filteredLocations.length === 0 ? /*#__PURE__*/_jsxDEV(TableRow, {\n              children: /*#__PURE__*/_jsxDEV(TableCell, {\n                colSpan: isAdmin ? 6 : 4,\n                align: \"center\",\n                children: isAdmin && (filters.status !== 'all' || filters.employee !== 'all') ? 'No locations match the current filters' : 'No location records found.'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 660,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 659,\n              columnNumber: 17\n            }, this) : filteredLocations.map(loc => /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [isAdmin && /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      fontWeight: 600\n                    },\n                    children: loc.employeeName\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 672,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    sx: {\n                      color: '#64748b'\n                    },\n                    children: [\"ID: \", loc.employeeId]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 675,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 671,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 670,\n                columnNumber: 23\n              }, this), isAdmin && /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: 2\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      flex: 1\n                    },\n                    children: loc.address\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 684,\n                    columnNumber: 27\n                  }, this), loc.status === 'checked-in' && /*#__PURE__*/_jsxDEV(IconButton, {\n                    color: \"primary\",\n                    onClick: () => handleViewLiveLocation(loc),\n                    size: \"small\",\n                    sx: {\n                      backgroundColor: 'rgba(59, 130, 246, 0.1)',\n                      '&:hover': {\n                        backgroundColor: 'rgba(59, 130, 246, 0.2)',\n                        transform: 'scale(1.1)'\n                      },\n                      transition: 'all 0.2s ease-in-out'\n                    },\n                    title: \"View Live Location on Google Maps\",\n                    children: /*#__PURE__*/_jsxDEV(LocationIcon, {\n                      sx: {\n                        color: '#3b82f6'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 703,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 689,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 683,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 682,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: loc.checkInTime\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 709,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: loc.checkOutTime || 'Not checked out yet'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 710,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: getStatusChip(loc.status)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 711,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"right\",\n                children: [!isAdmin && loc.status === 'checked-in' && /*#__PURE__*/_jsxDEV(IconButton, {\n                  color: \"primary\",\n                  onClick: () => handleCheckOut(loc.id),\n                  size: \"small\",\n                  sx: {\n                    '&:hover': {\n                      backgroundColor: 'rgba(59, 130, 246, 0.1)'\n                    }\n                  },\n                  children: /*#__PURE__*/_jsxDEV(CheckOutIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 725,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 715,\n                  columnNumber: 25\n                }, this), isAdmin && /*#__PURE__*/_jsxDEV(IconButton, {\n                  color: \"error\",\n                  onClick: () => handleDeleteLocation(loc.id),\n                  size: \"small\",\n                  sx: {\n                    '&:hover': {\n                      backgroundColor: 'rgba(239, 68, 68, 0.1)'\n                    }\n                  },\n                  title: \"Delete Location Record\",\n                  children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 742,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 731,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 712,\n                columnNumber: 21\n              }, this)]\n            }, loc.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 668,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 657,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 646,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 636,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 449,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 448,\n    columnNumber: 5\n  }, this);\n};\n_s(Locations, \"KkYk9KmwWII4PxyhRqU80UvfIpk=\");\n_c = Locations;\nexport default Locations;\nvar _c;\n$RefreshReg$(_c, \"Locations\");", "map": {"version": 3, "names": ["Logout", "CheckOutIcon", "Delete", "DeleteIcon", "MyLocation", "LocationIcon", "Box", "<PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Chip", "CircularProgress", "Container", "Grid", "IconButton", "MenuItem", "Paper", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "TextField", "Typography", "useContext", "useEffect", "useState", "toast", "AuthContext", "employeeService", "locationService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Locations", "_s", "isAdmin", "locations", "setLocations", "loading", "setLoading", "locationError", "setLocationError", "isCheckedIn", "setIsCheckedIn", "currentLocationId", "setCurrentLocationId", "filters", "setFilters", "status", "employee", "sortBy", "setSortBy", "sortOrder", "setSortOrder", "employees", "setEmployees", "fetchLocations", "response", "getAllLocations", "data", "success", "formattedLocations", "map", "loc", "_loc$employee", "_loc$employee$user", "_loc$employee2", "id", "_id", "latitude", "longitude", "address", "checkInTime", "Date", "toLocaleString", "checkOutTime", "employeeName", "user", "name", "employeeId", "currentUserCheckedIn", "find", "employeesResponse", "getAllEmployees", "formattedEmployees", "emp", "_emp$user", "empError", "console", "error", "handleInstantCheckIn", "navigator", "geolocation", "getCurrentPosition", "position", "location", "coords", "accuracy", "fetch", "json", "display_name", "toFixed", "handleCheckIn", "errorMessage", "code", "PERMISSION_DENIED", "POSITION_UNAVAILABLE", "TIMEOUT", "enableHighAccuracy", "timeout", "maximumAge", "addressText", "deviceInfo", "userAgent", "platform", "language", "locationData", "device", "split", "log", "checkIn", "startLiveLocationTracking", "window", "dispatchEvent", "CustomEvent", "localStorage", "setItem", "now", "toString", "Error", "request", "handleCheckOut", "checkOut", "stopLiveLocationTracking", "watchId", "setWatchId", "activeLocationId", "setActiveLocationId", "locationId", "watchPosition", "newLocation", "timestamp", "toISOString", "updateLiveLocation", "clearWatch", "handleDeleteLocation", "filter", "handleViewLiveLocation", "googleMapsUrl", "open", "autoClose", "hideProgressBar", "closeOnClick", "pauseOnHover", "draggable", "coordinates", "mapsUrl", "getStatusChip", "label", "size", "sx", "fontWeight", "backgroundColor", "color", "border", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getFilteredAndSortedLocations", "filtered", "statusMatch", "employeeMatch", "toLowerCase", "includes", "sort", "a", "b", "aValue", "bValue", "handleFilterChange", "filterType", "value", "prev", "filteredLocations", "max<PERSON><PERSON><PERSON>", "mt", "mb", "children", "p", "display", "justifyContent", "alignItems", "xs", "sm", "flexDirection", "gap", "variant", "component", "startIcon", "onClick", "disabled", "background", "alignSelf", "transform", "boxShadow", "transition", "bgcolor", "some", "gutterBottom", "borderRadius", "container", "spacing", "item", "md", "select", "fullWidth", "onChange", "e", "target", "borderColor", "borderWidth", "overflowX", "min<PERSON><PERSON><PERSON>", "align", "length", "colSpan", "flex", "title", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/EmployeeManagementSystem/frontend/src/pages/Locations.js"], "sourcesContent": ["import {\n    Logout as CheckOutIcon,\n    Delete as DeleteIcon,\n    MyLocation as LocationIcon\n} from '@mui/icons-material';\nimport {\n    Box,\n    Button,\n    Card,\n    CardContent,\n    Chip,\n    CircularProgress,\n    Container,\n    Grid,\n    IconButton,\n    MenuItem,\n    Paper,\n    Table,\n    TableBody,\n    TableCell,\n    TableContainer,\n    TableHead,\n    TableRow,\n    TextField,\n    Typography\n} from '@mui/material';\nimport { useContext, useEffect, useState } from 'react';\nimport { toast } from 'react-toastify';\nimport { AuthContext } from '../context/AuthContext';\nimport { employeeService, locationService } from '../services/api';\n\n// No mock data - fetch from database\n\nconst Locations = () => {\n  const { isAdmin } = useContext(AuthContext);\n  const [locations, setLocations] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [locationError, setLocationError] = useState(null);\n  const [isCheckedIn, setIsCheckedIn] = useState(false);\n  const [currentLocationId, setCurrentLocationId] = useState(null);\n\n  // Filtering and Sorting State (Admin only)\n  const [filters, setFilters] = useState({\n    status: 'all',\n    employee: 'all'\n  });\n  const [sortBy, setSortBy] = useState('checkInTime');\n  const [sortOrder, setSortOrder] = useState('desc');\n  const [employees, setEmployees] = useState([]);\n\n  // Fetch locations and employees function\n  const fetchLocations = async () => {\n    try {\n      const response = await locationService.getAllLocations();\n      if (response.data.success) {\n        const formattedLocations = response.data.data.map(loc => ({\n          id: loc._id,\n          latitude: loc.latitude, // Keep for Google Maps but don't display\n          longitude: loc.longitude, // Keep for Google Maps but don't display\n          address: loc.address,\n          checkInTime: new Date(loc.checkInTime).toLocaleString(),\n          checkOutTime: loc.checkOutTime ? new Date(loc.checkOutTime).toLocaleString() : null,\n          status: loc.status,\n          employeeName: loc.employee?.user?.name || 'Unknown',\n          employeeId: loc.employee?.employeeId || 'N/A'\n        }));\n        setLocations(formattedLocations);\n\n        // Check if current user (employee) is checked in\n        if (!isAdmin) {\n          const currentUserCheckedIn = formattedLocations.find(loc => loc.status === 'checked-in');\n          if (currentUserCheckedIn) {\n            setIsCheckedIn(true);\n            setCurrentLocationId(currentUserCheckedIn.id);\n          } else {\n            setIsCheckedIn(false);\n            setCurrentLocationId(null);\n          }\n        }\n      }\n\n      // Fetch employees for admin dropdown (only if admin)\n      if (isAdmin) {\n        try {\n          const employeesResponse = await employeeService.getAllEmployees();\n          if (employeesResponse.data.success) {\n            const formattedEmployees = employeesResponse.data.data.map(emp => ({\n              id: emp._id,\n              name: emp.user?.name || 'Unknown',\n              employeeId: emp.employeeId || 'N/A'\n            }));\n            setEmployees(formattedEmployees);\n          }\n        } catch (empError) {\n          console.error('Error fetching employees:', empError);\n          // Don't show error toast for employees as it's not critical\n        }\n      }\n    } catch (error) {\n      console.error('Error fetching locations:', error);\n      toast.error('Failed to fetch locations');\n    }\n  };\n\n  useEffect(() => {\n    fetchLocations();\n  }, [isAdmin]);\n\n  const handleInstantCheckIn = () => {\n    setLocationError(null);\n    setLoading(true);\n\n    // Get current location with high accuracy and check in immediately\n    if (navigator.geolocation) {\n      navigator.geolocation.getCurrentPosition(\n        async (position) => {\n          const location = {\n            latitude: position.coords.latitude,\n            longitude: position.coords.longitude,\n            accuracy: position.coords.accuracy\n          };\n\n          // Get address from coordinates using reverse geocoding\n          let address;\n          try {\n            // Using a simple reverse geocoding service\n            const response = await fetch(\n              `https://nominatim.openstreetmap.org/reverse?format=json&lat=${location.latitude}&lon=${location.longitude}`\n            );\n            const data = await response.json();\n\n            if (data && data.display_name) {\n              address = data.display_name;\n            } else {\n              address = `Location: ${location.latitude.toFixed(4)}, ${location.longitude.toFixed(4)}`;\n            }\n          } catch (error) {\n            // Fallback to coordinates if geocoding fails\n            address = `Location: ${location.latitude.toFixed(4)}, ${location.longitude.toFixed(4)}`;\n          }\n\n          // Immediately check in without confirmation\n          await handleCheckIn(location, address);\n        },\n        (error) => {\n          let errorMessage = 'Unable to get your location. ';\n          switch(error.code) {\n            case error.PERMISSION_DENIED:\n              errorMessage += 'Location access denied. Please enable location permissions.';\n              break;\n            case error.POSITION_UNAVAILABLE:\n              errorMessage += 'Location information unavailable.';\n              break;\n            case error.TIMEOUT:\n              errorMessage += 'Location request timed out.';\n              break;\n            default:\n              errorMessage += 'An unknown error occurred.';\n              break;\n          }\n          setLocationError(errorMessage);\n          setLoading(false);\n          toast.error(errorMessage);\n        },\n        {\n          enableHighAccuracy: true,\n          timeout: 10000,\n          maximumAge: 0\n        }\n      );\n    } else {\n      const errorMessage = 'Geolocation is not supported by this browser. Please use a modern browser.';\n      setLocationError(errorMessage);\n      setLoading(false);\n      toast.error(errorMessage);\n    }\n  };\n\n\n\n  const handleCheckIn = async (location, addressText) => {\n    if (!location) {\n      toast.error('Location not available. Please try again.');\n      setLoading(false);\n      return;\n    }\n\n    try {\n      // Get device and IP information\n      const deviceInfo = {\n        userAgent: navigator.userAgent,\n        platform: navigator.platform,\n        language: navigator.language\n      };\n\n      const locationData = {\n        latitude: location.latitude,\n        longitude: location.longitude,\n        address: addressText,\n        device: `${deviceInfo.platform} - ${deviceInfo.userAgent.split(' ')[0]}`,\n        accuracy: location.accuracy\n      };\n\n      console.log('Checking in with location data:', locationData);\n\n      const response = await locationService.checkIn(locationData);\n\n      if (response.data.success) {\n        // Update state immediately\n        setIsCheckedIn(true);\n        setCurrentLocationId(response.data.data._id);\n\n        // Refresh locations list\n        await fetchLocations();\n\n        // Show single success notification\n        toast.success(' Checked in successfully!');\n\n        // Start continuous location tracking\n        startLiveLocationTracking(response.data.data._id);\n\n        // Notify other components (like Dashboard) about location status change\n        window.dispatchEvent(new CustomEvent('locationStatusChanged'));\n        localStorage.setItem('locationStatusChanged', Date.now().toString());\n      } else {\n        throw new Error('Check-in failed');\n      }\n    } catch (error) {\n      console.error('Check-in error:', error);\n\n      if (error.response) {\n        toast.error(`Check-in failed: ${error.response.data.error || 'Server error'}`);\n      } else if (error.request) {\n        toast.error('No response from server. Please check if backend is running.');\n      } else {\n        toast.error('Failed to check in. Please try again.');\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCheckOut = async (id) => {\n    try {\n      setLoading(true);\n\n      const response = await locationService.checkOut(id);\n\n      if (response.data.success) {\n        // Update state immediately\n        setIsCheckedIn(false);\n        setCurrentLocationId(null);\n\n        // Refresh locations list\n        await fetchLocations();\n\n        // Show single success notification\n        toast.success('Successfully checked out! ');\n\n        // Stop live location tracking\n        stopLiveLocationTracking();\n      } else {\n        throw new Error('Check-out failed');\n      }\n    } catch (error) {\n      console.error('Check-out error:', error);\n\n      if (error.response) {\n        toast.error(`Check-out failed: ${error.response.data.error || 'Server error'}`);\n      } else if (error.request) {\n        toast.error('No response from server. Please check if backend is running.');\n      } else {\n        toast.error('Failed to check out. Please try again.');\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Live location tracking functions\n  const [watchId, setWatchId] = useState(null);\n  const [activeLocationId, setActiveLocationId] = useState(null);\n\n  const startLiveLocationTracking = (locationId) => {\n    if (navigator.geolocation && !watchId) {\n      setActiveLocationId(locationId);\n\n      const id = navigator.geolocation.watchPosition(\n        async (position) => {\n          const newLocation = {\n            latitude: position.coords.latitude,\n            longitude: position.coords.longitude,\n            accuracy: position.coords.accuracy,\n            timestamp: new Date().toISOString()\n          };\n\n          console.log('Live location update:', newLocation);\n\n          // Send real-time location update to server\n          try {\n            await locationService.updateLiveLocation(locationId, newLocation);\n            console.log('Location updated on server');\n          } catch (error) {\n            console.error('Failed to update location on server:', error);\n          }\n        },\n        (error) => {\n          console.error('Live location tracking error:', error);\n          toast.error('Location tracking error. Please check your GPS settings.');\n        },\n        {\n          enableHighAccuracy: true,\n          timeout: 15000,\n          maximumAge: 30000 // 30 seconds\n        }\n      );\n\n      setWatchId(id);\n      console.log('🔴 Live location tracking started for location:', locationId);\n    }\n  };\n\n  const stopLiveLocationTracking = () => {\n    if (watchId) {\n      navigator.geolocation.clearWatch(watchId);\n      setWatchId(null);\n      setActiveLocationId(null);\n      console.log('🔴 Live location tracking stopped');\n    }\n  };\n\n  // Clean up location tracking on component unmount\n  useEffect(() => {\n    return () => {\n      stopLiveLocationTracking();\n    };\n  }, [watchId]);\n\n  const handleDeleteLocation = (id) => {\n    setLocations(locations.filter(loc => loc.id !== id));\n    toast.success('Location record deleted successfully!');\n  };\n\n  // Handle viewing live location on Google Maps\n  const handleViewLiveLocation = (location) => {\n    const { latitude, longitude, employeeName, employeeId } = location;\n\n    // Create Google Maps URL with the employee's location (high zoom for precise location)\n    const googleMapsUrl = `https://www.google.com/maps?q=${latitude},${longitude}&z=19&t=h`;\n\n    // Open Google Maps in a new tab\n    window.open(googleMapsUrl, '_blank');\n\n    // Show success message with employee info\n    toast.success(`📍 Viewing ${employeeName}'s live location on Google Maps`, {\n      position: \"top-right\",\n      autoClose: 3000,\n      hideProgressBar: false,\n      closeOnClick: true,\n      pauseOnHover: true,\n      draggable: true,\n    });\n\n    console.log('📍 Google Maps opened for employee:', {\n      name: employeeName,\n      id: employeeId,\n      coordinates: `${latitude}, ${longitude}`,\n      mapsUrl: googleMapsUrl,\n      timestamp: new Date().toISOString()\n    });\n  };\n\n  const getStatusChip = (status) => {\n    return status === 'checked-in'\n      ? <Chip\n          label=\"Checked In\"\n          size=\"small\"\n          sx={{\n            fontWeight: 600,\n            backgroundColor: '#dcfce7', // Light green background\n            color: '#166534', // Dark green text\n            border: '1px solid #bbf7d0', // Light green border\n            '&:hover': {\n              backgroundColor: '#bbf7d0', // Slightly darker on hover\n            }\n          }}\n        />\n      : <Chip\n          label=\"Checked Out\"\n          size=\"small\"\n          sx={{\n            fontWeight: 500,\n            backgroundColor: '#f1f5f9', // Light gray background\n            color: '#64748b', // Gray text\n            border: '1px solid #e2e8f0' // Light gray border\n          }}\n        />;\n  };\n\n  // Filter and Sort Locations (Admin only)\n  const getFilteredAndSortedLocations = () => {\n    if (!isAdmin) return locations;\n\n    let filtered = locations.filter(loc => {\n      const statusMatch = filters.status === 'all' || loc.status === filters.status;\n      const employeeMatch = filters.employee === 'all' || loc.employeeName.toLowerCase().includes(filters.employee.toLowerCase());\n\n      return statusMatch && employeeMatch;\n    });\n\n    // Sort locations\n    filtered.sort((a, b) => {\n      let aValue = a[sortBy];\n      let bValue = b[sortBy];\n\n      // Handle date sorting\n      if (sortBy === 'checkInTime' || sortBy === 'checkOutTime') {\n        aValue = new Date(aValue || 0);\n        bValue = new Date(bValue || 0);\n      }\n\n      // Handle string sorting\n      if (typeof aValue === 'string') {\n        aValue = aValue.toLowerCase();\n        bValue = bValue.toLowerCase();\n      }\n\n      if (sortOrder === 'asc') {\n        return aValue > bValue ? 1 : -1;\n      } else {\n        return aValue < bValue ? 1 : -1;\n      }\n    });\n\n    return filtered;\n  };\n\n  const handleFilterChange = (filterType, value) => {\n    setFilters(prev => ({\n      ...prev,\n      [filterType]: value\n    }));\n  };\n\n  const filteredLocations = getFilteredAndSortedLocations();\n\n  return (\n    <Container maxWidth=\"lg\" sx={{ mt: 4, mb: 4 }}>\n      <Paper sx={{ p: 3 }}>\n        <Box sx={{\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: { xs: 'flex-start', sm: 'center' },\n          flexDirection: { xs: 'column', sm: 'row' },\n          gap: { xs: 2, sm: 0 },\n          mb: 3\n        }}>\n          <Typography variant=\"h5\" component=\"h1\">\n            Location Tracking\n          </Typography>\n          {!isAdmin && (\n            <>\n              {!isCheckedIn ? (\n                <Button\n                  variant=\"contained\"\n                  startIcon={loading ? <CircularProgress size={20} sx={{ color: '#ffffff' }} /> : <LocationIcon />}\n                  onClick={handleInstantCheckIn}\n                  disabled={loading}\n                  sx={{\n                    background: 'linear-gradient(135deg, #34d399 0%, #10b981 100%)',\n                    alignSelf: { xs: 'stretch', sm: 'auto' },\n                    '&:hover': {\n                      background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',\n                      transform: 'translateY(-1px)',\n                      boxShadow: '0 4px 12px rgba(52, 211, 153, 0.3)',\n                    },\n                    '&:disabled': {\n                      background: '#e5e7eb',\n                      color: '#9ca3af',\n                    },\n                    transition: 'all 0.2s ease-in-out'\n                  }}\n                >\n                  {loading ? 'Checking In...' : '📍 Check In'}\n                </Button>\n              ) : (\n                <Button\n                  variant=\"contained\"\n                  startIcon={loading ? <CircularProgress size={20} sx={{ color: '#ffffff' }} /> : <CheckOutIcon />}\n                  onClick={() => handleCheckOut(currentLocationId)}\n                  disabled={loading}\n                  sx={{\n                    background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n                    alignSelf: { xs: 'stretch', sm: 'auto' },\n                    '&:hover': {\n                      background: 'linear-gradient(135deg, #dc2626 0%, #b91c1c 100%)',\n                      transform: 'translateY(-1px)',\n                      boxShadow: '0 4px 12px rgba(239, 68, 68, 0.3)',\n                    },\n                    '&:disabled': {\n                      background: '#e5e7eb',\n                      color: '#9ca3af',\n                    },\n                    transition: 'all 0.2s ease-in-out'\n                  }}\n                >\n                  {loading ? 'Checking Out...' : '🔴 Check Out'}\n                </Button>\n              )}\n            </>\n          )}\n        </Box>\n\n        {locationError && (\n          <Paper sx={{ p: 2, mb: 3, bgcolor: 'error.light' }}>\n            <Typography color=\"error\">{locationError}</Typography>\n          </Paper>\n        )}\n\n        {!isAdmin && locations.some(loc => loc.status === 'checked-in') && (\n          <Card sx={{ mb: 3, bgcolor: 'success.light' }}>\n            <CardContent>\n              <Typography variant=\"h6\" gutterBottom>\n                You are currently checked in\n              </Typography>\n              <Typography variant=\"body2\">\n                Don't forget to check out at the end of your work day.\n              </Typography>\n            </CardContent>\n          </Card>\n        )}\n\n        {/* Admin Filters and Sorting */}\n        {isAdmin && (\n          <Box sx={{ mb: 3, p: 2, bgcolor: '#f8fafc', borderRadius: 2 }}>\n            <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600 }}>\n              Filter & Sort Locations\n            </Typography>\n            <Grid container spacing={2}>\n              <Grid item xs={12} sm={6} md={4}>\n                <TextField\n                  select\n                  fullWidth\n                  label=\"Status\"\n                  value={filters.status}\n                  onChange={(e) => handleFilterChange('status', e.target.value)}\n                  size=\"small\"\n                >\n                  <MenuItem value=\"all\">All Status</MenuItem>\n                  <MenuItem value=\"checked-in\">Checked In</MenuItem>\n                  <MenuItem value=\"checked-out\">Checked Out</MenuItem>\n                </TextField>\n              </Grid>\n              <Grid item xs={12} sm={6} md={4}>\n                <TextField\n                  select\n                  fullWidth\n                  label=\"Filter by Employee\"\n                  value={filters.employee}\n                  onChange={(e) => handleFilterChange('employee', e.target.value)}\n                  size=\"small\"\n                  sx={{\n                    '& .MuiOutlinedInput-root': {\n                      borderRadius: 2,\n                      backgroundColor: '#ffffff',\n                      '&:hover fieldset': {\n                        borderColor: '#3b82f6',\n                      },\n                      '&.Mui-focused fieldset': {\n                        borderColor: '#1e40af',\n                        borderWidth: '2px',\n                      },\n                    },\n                    '& .MuiInputLabel-root': {\n                      '&.Mui-focused': {\n                        color: '#1e40af',\n                        fontWeight: 600,\n                      },\n                    },\n                  }}\n                >\n                  <MenuItem value=\"all\">All Employees</MenuItem>\n                  {employees.map((employee) => (\n                    <MenuItem key={employee.id} value={employee.name}>\n                      <Box sx={{ display: 'flex', flexDirection: 'column' }}>\n                        <Typography variant=\"body2\" sx={{ fontWeight: 500 }}>\n                          {employee.name}\n                        </Typography>\n                        <Typography variant=\"caption\" color=\"text.secondary\">\n                          ID: {employee.employeeId}\n                        </Typography>\n                      </Box>\n                    </MenuItem>\n                  ))}\n                </TextField>\n              </Grid>\n              <Grid item xs={12} sm={6} md={4}>\n                <TextField\n                  select\n                  fullWidth\n                  label=\"Sort By\"\n                  value={sortBy}\n                  onChange={(e) => setSortBy(e.target.value)}\n                  size=\"small\"\n                >\n                  <MenuItem value=\"checkInTime\">Check In Time</MenuItem>\n                  <MenuItem value=\"checkOutTime\">Check Out Time</MenuItem>\n                  <MenuItem value=\"employeeName\">Employee Name</MenuItem>\n                  <MenuItem value=\"status\">Status</MenuItem>\n                </TextField>\n              </Grid>\n            </Grid>\n            <Box sx={{ mt: 2, display: 'flex', gap: 1 }}>\n              <Button\n                variant=\"outlined\"\n                size=\"small\"\n                onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}\n              >\n                {sortOrder === 'asc' ? '↑ Ascending' : '↓ Descending'}\n              </Button>\n              <Button\n                variant=\"outlined\"\n                size=\"small\"\n                onClick={() => {\n                  setFilters({ status: 'all', employee: 'all' });\n                  setSortBy('checkInTime');\n                  setSortOrder('desc');\n                }}\n              >\n                Clear Filters\n              </Button>\n            </Box>\n          </Box>\n        )}\n\n        <TableContainer\n          component={Paper}\n          variant=\"outlined\"\n          sx={{\n            overflowX: 'auto',\n            '& .MuiTable-root': {\n              minWidth: { xs: 300, sm: 650 }\n            }\n          }}\n        >\n          <Table>\n            <TableHead>\n              <TableRow>\n                {isAdmin && <TableCell>Employee</TableCell>}\n                {isAdmin && <TableCell>Location</TableCell>}\n                <TableCell>Check In Time</TableCell>\n                <TableCell>Check Out Time</TableCell>\n                <TableCell>Status</TableCell>\n                <TableCell align=\"right\">Actions</TableCell>\n              </TableRow>\n            </TableHead>\n            <TableBody>\n              {filteredLocations.length === 0 ? (\n                <TableRow>\n                  <TableCell colSpan={isAdmin ? 6 : 4} align=\"center\">\n                    {isAdmin && (filters.status !== 'all' || filters.employee !== 'all')\n                      ? 'No locations match the current filters'\n                      : 'No location records found.'}\n                  </TableCell>\n                </TableRow>\n              ) : (\n                filteredLocations.map((loc) => (\n                  <TableRow key={loc.id}>\n                    {isAdmin && (\n                      <TableCell>\n                        <Box>\n                          <Typography variant=\"body2\" sx={{ fontWeight: 600 }}>\n                            {loc.employeeName}\n                          </Typography>\n                          <Typography variant=\"caption\" sx={{ color: '#64748b' }}>\n                            ID: {loc.employeeId}\n                          </Typography>\n                        </Box>\n                      </TableCell>\n                    )}\n                    {isAdmin && (\n                      <TableCell>\n                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                          <Typography variant=\"body2\" sx={{ flex: 1 }}>\n                            {loc.address}\n                          </Typography>\n                          {/* Location Icon for Direct Google Maps Access */}\n                          {loc.status === 'checked-in' && (\n                            <IconButton\n                              color=\"primary\"\n                              onClick={() => handleViewLiveLocation(loc)}\n                              size=\"small\"\n                              sx={{\n                                backgroundColor: 'rgba(59, 130, 246, 0.1)',\n                                '&:hover': {\n                                  backgroundColor: 'rgba(59, 130, 246, 0.2)',\n                                  transform: 'scale(1.1)',\n                                },\n                                transition: 'all 0.2s ease-in-out'\n                              }}\n                              title=\"View Live Location on Google Maps\"\n                            >\n                              <LocationIcon sx={{ color: '#3b82f6' }} />\n                            </IconButton>\n                          )}\n                        </Box>\n                      </TableCell>\n                    )}\n                    <TableCell>{loc.checkInTime}</TableCell>\n                    <TableCell>{loc.checkOutTime || 'Not checked out yet'}</TableCell>\n                    <TableCell>{getStatusChip(loc.status)}</TableCell>\n                    <TableCell align=\"right\">\n                      {/* Employee Actions */}\n                      {!isAdmin && loc.status === 'checked-in' && (\n                        <IconButton\n                          color=\"primary\"\n                          onClick={() => handleCheckOut(loc.id)}\n                          size=\"small\"\n                          sx={{\n                            '&:hover': {\n                              backgroundColor: 'rgba(59, 130, 246, 0.1)',\n                            },\n                          }}\n                        >\n                          <CheckOutIcon />\n                        </IconButton>\n                      )}\n\n                      {/* Admin Actions */}\n                      {isAdmin && (\n                        <IconButton\n                          color=\"error\"\n                          onClick={() => handleDeleteLocation(loc.id)}\n                          size=\"small\"\n                          sx={{\n                            '&:hover': {\n                              backgroundColor: 'rgba(239, 68, 68, 0.1)',\n                            },\n                          }}\n                          title=\"Delete Location Record\"\n                        >\n                          <DeleteIcon />\n                        </IconButton>\n                      )}\n                    </TableCell>\n                  </TableRow>\n                ))\n              )}\n            </TableBody>\n          </Table>\n        </TableContainer>\n      </Paper>\n\n\n    </Container>\n  );\n};\n\nexport default Locations;\n"], "mappings": ";;AAAA,SACIA,MAAM,IAAIC,YAAY,EACtBC,MAAM,IAAIC,UAAU,EACpBC,UAAU,IAAIC,YAAY,QACvB,qBAAqB;AAC5B,SACIC,GAAG,EACHC,MAAM,EACNC,IAAI,EACJC,WAAW,EACXC,IAAI,EACJC,gBAAgB,EAChBC,SAAS,EACTC,IAAI,EACJC,UAAU,EACVC,QAAQ,EACRC,KAAK,EACLC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,SAAS,EACTC,UAAU,QACP,eAAe;AACtB,SAASC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AACvD,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,WAAW,QAAQ,wBAAwB;AACpD,SAASC,eAAe,EAAEC,eAAe,QAAQ,iBAAiB;;AAElE;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEA,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM;IAAEC;EAAQ,CAAC,GAAGb,UAAU,CAACI,WAAW,CAAC;EAC3C,MAAM,CAACU,SAAS,EAAEC,YAAY,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACgB,aAAa,EAAEC,gBAAgB,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACkB,WAAW,EAAEC,cAAc,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACoB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;;EAEhE;EACA,MAAM,CAACsB,OAAO,EAAEC,UAAU,CAAC,GAAGvB,QAAQ,CAAC;IACrCwB,MAAM,EAAE,KAAK;IACbC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG3B,QAAQ,CAAC,aAAa,CAAC;EACnD,MAAM,CAAC4B,SAAS,EAAEC,YAAY,CAAC,GAAG7B,QAAQ,CAAC,MAAM,CAAC;EAClD,MAAM,CAAC8B,SAAS,EAAEC,YAAY,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;;EAE9C;EACA,MAAMgC,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAM7B,eAAe,CAAC8B,eAAe,CAAC,CAAC;MACxD,IAAID,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzB,MAAMC,kBAAkB,GAAGJ,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACG,GAAG,CAACC,GAAG;UAAA,IAAAC,aAAA,EAAAC,kBAAA,EAAAC,cAAA;UAAA,OAAK;YACxDC,EAAE,EAAEJ,GAAG,CAACK,GAAG;YACXC,QAAQ,EAAEN,GAAG,CAACM,QAAQ;YAAE;YACxBC,SAAS,EAAEP,GAAG,CAACO,SAAS;YAAE;YAC1BC,OAAO,EAAER,GAAG,CAACQ,OAAO;YACpBC,WAAW,EAAE,IAAIC,IAAI,CAACV,GAAG,CAACS,WAAW,CAAC,CAACE,cAAc,CAAC,CAAC;YACvDC,YAAY,EAAEZ,GAAG,CAACY,YAAY,GAAG,IAAIF,IAAI,CAACV,GAAG,CAACY,YAAY,CAAC,CAACD,cAAc,CAAC,CAAC,GAAG,IAAI;YACnF1B,MAAM,EAAEe,GAAG,CAACf,MAAM;YAClB4B,YAAY,EAAE,EAAAZ,aAAA,GAAAD,GAAG,CAACd,QAAQ,cAAAe,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAca,IAAI,cAAAZ,kBAAA,uBAAlBA,kBAAA,CAAoBa,IAAI,KAAI,SAAS;YACnDC,UAAU,EAAE,EAAAb,cAAA,GAAAH,GAAG,CAACd,QAAQ,cAAAiB,cAAA,uBAAZA,cAAA,CAAca,UAAU,KAAI;UAC1C,CAAC;QAAA,CAAC,CAAC;QACH1C,YAAY,CAACwB,kBAAkB,CAAC;;QAEhC;QACA,IAAI,CAAC1B,OAAO,EAAE;UACZ,MAAM6C,oBAAoB,GAAGnB,kBAAkB,CAACoB,IAAI,CAAClB,GAAG,IAAIA,GAAG,CAACf,MAAM,KAAK,YAAY,CAAC;UACxF,IAAIgC,oBAAoB,EAAE;YACxBrC,cAAc,CAAC,IAAI,CAAC;YACpBE,oBAAoB,CAACmC,oBAAoB,CAACb,EAAE,CAAC;UAC/C,CAAC,MAAM;YACLxB,cAAc,CAAC,KAAK,CAAC;YACrBE,oBAAoB,CAAC,IAAI,CAAC;UAC5B;QACF;MACF;;MAEA;MACA,IAAIV,OAAO,EAAE;QACX,IAAI;UACF,MAAM+C,iBAAiB,GAAG,MAAMvD,eAAe,CAACwD,eAAe,CAAC,CAAC;UACjE,IAAID,iBAAiB,CAACvB,IAAI,CAACC,OAAO,EAAE;YAClC,MAAMwB,kBAAkB,GAAGF,iBAAiB,CAACvB,IAAI,CAACA,IAAI,CAACG,GAAG,CAACuB,GAAG;cAAA,IAAAC,SAAA;cAAA,OAAK;gBACjEnB,EAAE,EAAEkB,GAAG,CAACjB,GAAG;gBACXU,IAAI,EAAE,EAAAQ,SAAA,GAAAD,GAAG,CAACR,IAAI,cAAAS,SAAA,uBAARA,SAAA,CAAUR,IAAI,KAAI,SAAS;gBACjCC,UAAU,EAAEM,GAAG,CAACN,UAAU,IAAI;cAChC,CAAC;YAAA,CAAC,CAAC;YACHxB,YAAY,CAAC6B,kBAAkB,CAAC;UAClC;QACF,CAAC,CAAC,OAAOG,QAAQ,EAAE;UACjBC,OAAO,CAACC,KAAK,CAAC,2BAA2B,EAAEF,QAAQ,CAAC;UACpD;QACF;MACF;IACF,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDhE,KAAK,CAACgE,KAAK,CAAC,2BAA2B,CAAC;IAC1C;EACF,CAAC;EAEDlE,SAAS,CAAC,MAAM;IACdiC,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACrB,OAAO,CAAC,CAAC;EAEb,MAAMuD,oBAAoB,GAAGA,CAAA,KAAM;IACjCjD,gBAAgB,CAAC,IAAI,CAAC;IACtBF,UAAU,CAAC,IAAI,CAAC;;IAEhB;IACA,IAAIoD,SAAS,CAACC,WAAW,EAAE;MACzBD,SAAS,CAACC,WAAW,CAACC,kBAAkB,CACtC,MAAOC,QAAQ,IAAK;QAClB,MAAMC,QAAQ,GAAG;UACf1B,QAAQ,EAAEyB,QAAQ,CAACE,MAAM,CAAC3B,QAAQ;UAClCC,SAAS,EAAEwB,QAAQ,CAACE,MAAM,CAAC1B,SAAS;UACpC2B,QAAQ,EAAEH,QAAQ,CAACE,MAAM,CAACC;QAC5B,CAAC;;QAED;QACA,IAAI1B,OAAO;QACX,IAAI;UACF;UACA,MAAMd,QAAQ,GAAG,MAAMyC,KAAK,CAC1B,+DAA+DH,QAAQ,CAAC1B,QAAQ,QAAQ0B,QAAQ,CAACzB,SAAS,EAC5G,CAAC;UACD,MAAMX,IAAI,GAAG,MAAMF,QAAQ,CAAC0C,IAAI,CAAC,CAAC;UAElC,IAAIxC,IAAI,IAAIA,IAAI,CAACyC,YAAY,EAAE;YAC7B7B,OAAO,GAAGZ,IAAI,CAACyC,YAAY;UAC7B,CAAC,MAAM;YACL7B,OAAO,GAAG,aAAawB,QAAQ,CAAC1B,QAAQ,CAACgC,OAAO,CAAC,CAAC,CAAC,KAAKN,QAAQ,CAACzB,SAAS,CAAC+B,OAAO,CAAC,CAAC,CAAC,EAAE;UACzF;QACF,CAAC,CAAC,OAAOZ,KAAK,EAAE;UACd;UACAlB,OAAO,GAAG,aAAawB,QAAQ,CAAC1B,QAAQ,CAACgC,OAAO,CAAC,CAAC,CAAC,KAAKN,QAAQ,CAACzB,SAAS,CAAC+B,OAAO,CAAC,CAAC,CAAC,EAAE;QACzF;;QAEA;QACA,MAAMC,aAAa,CAACP,QAAQ,EAAExB,OAAO,CAAC;MACxC,CAAC,EACAkB,KAAK,IAAK;QACT,IAAIc,YAAY,GAAG,+BAA+B;QAClD,QAAOd,KAAK,CAACe,IAAI;UACf,KAAKf,KAAK,CAACgB,iBAAiB;YAC1BF,YAAY,IAAI,6DAA6D;YAC7E;UACF,KAAKd,KAAK,CAACiB,oBAAoB;YAC7BH,YAAY,IAAI,mCAAmC;YACnD;UACF,KAAKd,KAAK,CAACkB,OAAO;YAChBJ,YAAY,IAAI,6BAA6B;YAC7C;UACF;YACEA,YAAY,IAAI,4BAA4B;YAC5C;QACJ;QACA9D,gBAAgB,CAAC8D,YAAY,CAAC;QAC9BhE,UAAU,CAAC,KAAK,CAAC;QACjBd,KAAK,CAACgE,KAAK,CAACc,YAAY,CAAC;MAC3B,CAAC,EACD;QACEK,kBAAkB,EAAE,IAAI;QACxBC,OAAO,EAAE,KAAK;QACdC,UAAU,EAAE;MACd,CACF,CAAC;IACH,CAAC,MAAM;MACL,MAAMP,YAAY,GAAG,4EAA4E;MACjG9D,gBAAgB,CAAC8D,YAAY,CAAC;MAC9BhE,UAAU,CAAC,KAAK,CAAC;MACjBd,KAAK,CAACgE,KAAK,CAACc,YAAY,CAAC;IAC3B;EACF,CAAC;EAID,MAAMD,aAAa,GAAG,MAAAA,CAAOP,QAAQ,EAAEgB,WAAW,KAAK;IACrD,IAAI,CAAChB,QAAQ,EAAE;MACbtE,KAAK,CAACgE,KAAK,CAAC,2CAA2C,CAAC;MACxDlD,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;IAEA,IAAI;MACF;MACA,MAAMyE,UAAU,GAAG;QACjBC,SAAS,EAAEtB,SAAS,CAACsB,SAAS;QAC9BC,QAAQ,EAAEvB,SAAS,CAACuB,QAAQ;QAC5BC,QAAQ,EAAExB,SAAS,CAACwB;MACtB,CAAC;MAED,MAAMC,YAAY,GAAG;QACnB/C,QAAQ,EAAE0B,QAAQ,CAAC1B,QAAQ;QAC3BC,SAAS,EAAEyB,QAAQ,CAACzB,SAAS;QAC7BC,OAAO,EAAEwC,WAAW;QACpBM,MAAM,EAAE,GAAGL,UAAU,CAACE,QAAQ,MAAMF,UAAU,CAACC,SAAS,CAACK,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;QACxErB,QAAQ,EAAEF,QAAQ,CAACE;MACrB,CAAC;MAEDT,OAAO,CAAC+B,GAAG,CAAC,iCAAiC,EAAEH,YAAY,CAAC;MAE5D,MAAM3D,QAAQ,GAAG,MAAM7B,eAAe,CAAC4F,OAAO,CAACJ,YAAY,CAAC;MAE5D,IAAI3D,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzB;QACAjB,cAAc,CAAC,IAAI,CAAC;QACpBE,oBAAoB,CAACY,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACS,GAAG,CAAC;;QAE5C;QACA,MAAMZ,cAAc,CAAC,CAAC;;QAEtB;QACA/B,KAAK,CAACmC,OAAO,CAAC,2BAA2B,CAAC;;QAE1C;QACA6D,yBAAyB,CAAChE,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACS,GAAG,CAAC;;QAEjD;QACAsD,MAAM,CAACC,aAAa,CAAC,IAAIC,WAAW,CAAC,uBAAuB,CAAC,CAAC;QAC9DC,YAAY,CAACC,OAAO,CAAC,uBAAuB,EAAErD,IAAI,CAACsD,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAC;MACtE,CAAC,MAAM;QACL,MAAM,IAAIC,KAAK,CAAC,iBAAiB,CAAC;MACpC;IACF,CAAC,CAAC,OAAOxC,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;MAEvC,IAAIA,KAAK,CAAChC,QAAQ,EAAE;QAClBhC,KAAK,CAACgE,KAAK,CAAC,oBAAoBA,KAAK,CAAChC,QAAQ,CAACE,IAAI,CAAC8B,KAAK,IAAI,cAAc,EAAE,CAAC;MAChF,CAAC,MAAM,IAAIA,KAAK,CAACyC,OAAO,EAAE;QACxBzG,KAAK,CAACgE,KAAK,CAAC,8DAA8D,CAAC;MAC7E,CAAC,MAAM;QACLhE,KAAK,CAACgE,KAAK,CAAC,uCAAuC,CAAC;MACtD;IACF,CAAC,SAAS;MACRlD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM4F,cAAc,GAAG,MAAOhE,EAAE,IAAK;IACnC,IAAI;MACF5B,UAAU,CAAC,IAAI,CAAC;MAEhB,MAAMkB,QAAQ,GAAG,MAAM7B,eAAe,CAACwG,QAAQ,CAACjE,EAAE,CAAC;MAEnD,IAAIV,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzB;QACAjB,cAAc,CAAC,KAAK,CAAC;QACrBE,oBAAoB,CAAC,IAAI,CAAC;;QAE1B;QACA,MAAMW,cAAc,CAAC,CAAC;;QAEtB;QACA/B,KAAK,CAACmC,OAAO,CAAC,4BAA4B,CAAC;;QAE3C;QACAyE,wBAAwB,CAAC,CAAC;MAC5B,CAAC,MAAM;QACL,MAAM,IAAIJ,KAAK,CAAC,kBAAkB,CAAC;MACrC;IACF,CAAC,CAAC,OAAOxC,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MAExC,IAAIA,KAAK,CAAChC,QAAQ,EAAE;QAClBhC,KAAK,CAACgE,KAAK,CAAC,qBAAqBA,KAAK,CAAChC,QAAQ,CAACE,IAAI,CAAC8B,KAAK,IAAI,cAAc,EAAE,CAAC;MACjF,CAAC,MAAM,IAAIA,KAAK,CAACyC,OAAO,EAAE;QACxBzG,KAAK,CAACgE,KAAK,CAAC,8DAA8D,CAAC;MAC7E,CAAC,MAAM;QACLhE,KAAK,CAACgE,KAAK,CAAC,wCAAwC,CAAC;MACvD;IACF,CAAC,SAAS;MACRlD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM,CAAC+F,OAAO,EAAEC,UAAU,CAAC,GAAG/G,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgH,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjH,QAAQ,CAAC,IAAI,CAAC;EAE9D,MAAMiG,yBAAyB,GAAIiB,UAAU,IAAK;IAChD,IAAI/C,SAAS,CAACC,WAAW,IAAI,CAAC0C,OAAO,EAAE;MACrCG,mBAAmB,CAACC,UAAU,CAAC;MAE/B,MAAMvE,EAAE,GAAGwB,SAAS,CAACC,WAAW,CAAC+C,aAAa,CAC5C,MAAO7C,QAAQ,IAAK;QAClB,MAAM8C,WAAW,GAAG;UAClBvE,QAAQ,EAAEyB,QAAQ,CAACE,MAAM,CAAC3B,QAAQ;UAClCC,SAAS,EAAEwB,QAAQ,CAACE,MAAM,CAAC1B,SAAS;UACpC2B,QAAQ,EAAEH,QAAQ,CAACE,MAAM,CAACC,QAAQ;UAClC4C,SAAS,EAAE,IAAIpE,IAAI,CAAC,CAAC,CAACqE,WAAW,CAAC;QACpC,CAAC;QAEDtD,OAAO,CAAC+B,GAAG,CAAC,uBAAuB,EAAEqB,WAAW,CAAC;;QAEjD;QACA,IAAI;UACF,MAAMhH,eAAe,CAACmH,kBAAkB,CAACL,UAAU,EAAEE,WAAW,CAAC;UACjEpD,OAAO,CAAC+B,GAAG,CAAC,4BAA4B,CAAC;QAC3C,CAAC,CAAC,OAAO9B,KAAK,EAAE;UACdD,OAAO,CAACC,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;QAC9D;MACF,CAAC,EACAA,KAAK,IAAK;QACTD,OAAO,CAACC,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrDhE,KAAK,CAACgE,KAAK,CAAC,0DAA0D,CAAC;MACzE,CAAC,EACD;QACEmB,kBAAkB,EAAE,IAAI;QACxBC,OAAO,EAAE,KAAK;QACdC,UAAU,EAAE,KAAK,CAAC;MACpB,CACF,CAAC;MAEDyB,UAAU,CAACpE,EAAE,CAAC;MACdqB,OAAO,CAAC+B,GAAG,CAAC,iDAAiD,EAAEmB,UAAU,CAAC;IAC5E;EACF,CAAC;EAED,MAAML,wBAAwB,GAAGA,CAAA,KAAM;IACrC,IAAIC,OAAO,EAAE;MACX3C,SAAS,CAACC,WAAW,CAACoD,UAAU,CAACV,OAAO,CAAC;MACzCC,UAAU,CAAC,IAAI,CAAC;MAChBE,mBAAmB,CAAC,IAAI,CAAC;MACzBjD,OAAO,CAAC+B,GAAG,CAAC,mCAAmC,CAAC;IAClD;EACF,CAAC;;EAED;EACAhG,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX8G,wBAAwB,CAAC,CAAC;IAC5B,CAAC;EACH,CAAC,EAAE,CAACC,OAAO,CAAC,CAAC;EAEb,MAAMW,oBAAoB,GAAI9E,EAAE,IAAK;IACnC9B,YAAY,CAACD,SAAS,CAAC8G,MAAM,CAACnF,GAAG,IAAIA,GAAG,CAACI,EAAE,KAAKA,EAAE,CAAC,CAAC;IACpD1C,KAAK,CAACmC,OAAO,CAAC,uCAAuC,CAAC;EACxD,CAAC;;EAED;EACA,MAAMuF,sBAAsB,GAAIpD,QAAQ,IAAK;IAC3C,MAAM;MAAE1B,QAAQ;MAAEC,SAAS;MAAEM,YAAY;MAAEG;IAAW,CAAC,GAAGgB,QAAQ;;IAElE;IACA,MAAMqD,aAAa,GAAG,iCAAiC/E,QAAQ,IAAIC,SAAS,WAAW;;IAEvF;IACAoD,MAAM,CAAC2B,IAAI,CAACD,aAAa,EAAE,QAAQ,CAAC;;IAEpC;IACA3H,KAAK,CAACmC,OAAO,CAAC,cAAcgB,YAAY,iCAAiC,EAAE;MACzEkB,QAAQ,EAAE,WAAW;MACrBwD,SAAS,EAAE,IAAI;MACfC,eAAe,EAAE,KAAK;MACtBC,YAAY,EAAE,IAAI;MAClBC,YAAY,EAAE,IAAI;MAClBC,SAAS,EAAE;IACb,CAAC,CAAC;IAEFlE,OAAO,CAAC+B,GAAG,CAAC,qCAAqC,EAAE;MACjDzC,IAAI,EAAEF,YAAY;MAClBT,EAAE,EAAEY,UAAU;MACd4E,WAAW,EAAE,GAAGtF,QAAQ,KAAKC,SAAS,EAAE;MACxCsF,OAAO,EAAER,aAAa;MACtBP,SAAS,EAAE,IAAIpE,IAAI,CAAC,CAAC,CAACqE,WAAW,CAAC;IACpC,CAAC,CAAC;EACJ,CAAC;EAED,MAAMe,aAAa,GAAI7G,MAAM,IAAK;IAChC,OAAOA,MAAM,KAAK,YAAY,gBAC1BlB,OAAA,CAACvB,IAAI;MACHuJ,KAAK,EAAC,YAAY;MAClBC,IAAI,EAAC,OAAO;MACZC,EAAE,EAAE;QACFC,UAAU,EAAE,GAAG;QACfC,eAAe,EAAE,SAAS;QAAE;QAC5BC,KAAK,EAAE,SAAS;QAAE;QAClBC,MAAM,EAAE,mBAAmB;QAAE;QAC7B,SAAS,EAAE;UACTF,eAAe,EAAE,SAAS,CAAE;QAC9B;MACF;IAAE;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,gBACF1I,OAAA,CAACvB,IAAI;MACHuJ,KAAK,EAAC,aAAa;MACnBC,IAAI,EAAC,OAAO;MACZC,EAAE,EAAE;QACFC,UAAU,EAAE,GAAG;QACfC,eAAe,EAAE,SAAS;QAAE;QAC5BC,KAAK,EAAE,SAAS;QAAE;QAClBC,MAAM,EAAE,mBAAmB,CAAC;MAC9B;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EACR,CAAC;;EAED;EACA,MAAMC,6BAA6B,GAAGA,CAAA,KAAM;IAC1C,IAAI,CAACtI,OAAO,EAAE,OAAOC,SAAS;IAE9B,IAAIsI,QAAQ,GAAGtI,SAAS,CAAC8G,MAAM,CAACnF,GAAG,IAAI;MACrC,MAAM4G,WAAW,GAAG7H,OAAO,CAACE,MAAM,KAAK,KAAK,IAAIe,GAAG,CAACf,MAAM,KAAKF,OAAO,CAACE,MAAM;MAC7E,MAAM4H,aAAa,GAAG9H,OAAO,CAACG,QAAQ,KAAK,KAAK,IAAIc,GAAG,CAACa,YAAY,CAACiG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAChI,OAAO,CAACG,QAAQ,CAAC4H,WAAW,CAAC,CAAC,CAAC;MAE3H,OAAOF,WAAW,IAAIC,aAAa;IACrC,CAAC,CAAC;;IAEF;IACAF,QAAQ,CAACK,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MACtB,IAAIC,MAAM,GAAGF,CAAC,CAAC9H,MAAM,CAAC;MACtB,IAAIiI,MAAM,GAAGF,CAAC,CAAC/H,MAAM,CAAC;;MAEtB;MACA,IAAIA,MAAM,KAAK,aAAa,IAAIA,MAAM,KAAK,cAAc,EAAE;QACzDgI,MAAM,GAAG,IAAIzG,IAAI,CAACyG,MAAM,IAAI,CAAC,CAAC;QAC9BC,MAAM,GAAG,IAAI1G,IAAI,CAAC0G,MAAM,IAAI,CAAC,CAAC;MAChC;;MAEA;MACA,IAAI,OAAOD,MAAM,KAAK,QAAQ,EAAE;QAC9BA,MAAM,GAAGA,MAAM,CAACL,WAAW,CAAC,CAAC;QAC7BM,MAAM,GAAGA,MAAM,CAACN,WAAW,CAAC,CAAC;MAC/B;MAEA,IAAIzH,SAAS,KAAK,KAAK,EAAE;QACvB,OAAO8H,MAAM,GAAGC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;MACjC,CAAC,MAAM;QACL,OAAOD,MAAM,GAAGC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;MACjC;IACF,CAAC,CAAC;IAEF,OAAOT,QAAQ;EACjB,CAAC;EAED,MAAMU,kBAAkB,GAAGA,CAACC,UAAU,EAAEC,KAAK,KAAK;IAChDvI,UAAU,CAACwI,IAAI,KAAK;MAClB,GAAGA,IAAI;MACP,CAACF,UAAU,GAAGC;IAChB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,iBAAiB,GAAGf,6BAA6B,CAAC,CAAC;EAEzD,oBACE3I,OAAA,CAACrB,SAAS;IAACgL,QAAQ,EAAC,IAAI;IAACzB,EAAE,EAAE;MAAE0B,EAAE,EAAE,CAAC;MAAEC,EAAE,EAAE;IAAE,CAAE;IAAAC,QAAA,eAC5C9J,OAAA,CAACjB,KAAK;MAACmJ,EAAE,EAAE;QAAE6B,CAAC,EAAE;MAAE,CAAE;MAAAD,QAAA,gBAClB9J,OAAA,CAAC3B,GAAG;QAAC6J,EAAE,EAAE;UACP8B,OAAO,EAAE,MAAM;UACfC,cAAc,EAAE,eAAe;UAC/BC,UAAU,EAAE;YAAEC,EAAE,EAAE,YAAY;YAAEC,EAAE,EAAE;UAAS,CAAC;UAC9CC,aAAa,EAAE;YAAEF,EAAE,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAM,CAAC;UAC1CE,GAAG,EAAE;YAAEH,EAAE,EAAE,CAAC;YAAEC,EAAE,EAAE;UAAE,CAAC;UACrBP,EAAE,EAAE;QACN,CAAE;QAAAC,QAAA,gBACA9J,OAAA,CAACT,UAAU;UAACgL,OAAO,EAAC,IAAI;UAACC,SAAS,EAAC,IAAI;UAAAV,QAAA,EAAC;QAExC;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EACZ,CAACrI,OAAO,iBACPL,OAAA,CAAAE,SAAA;UAAA4J,QAAA,EACG,CAAClJ,WAAW,gBACXZ,OAAA,CAAC1B,MAAM;YACLiM,OAAO,EAAC,WAAW;YACnBE,SAAS,EAAEjK,OAAO,gBAAGR,OAAA,CAACtB,gBAAgB;cAACuJ,IAAI,EAAE,EAAG;cAACC,EAAE,EAAE;gBAAEG,KAAK,EAAE;cAAU;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAG1I,OAAA,CAAC5B,YAAY;cAAAmK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACjGgC,OAAO,EAAE9G,oBAAqB;YAC9B+G,QAAQ,EAAEnK,OAAQ;YAClB0H,EAAE,EAAE;cACF0C,UAAU,EAAE,mDAAmD;cAC/DC,SAAS,EAAE;gBAAEV,EAAE,EAAE,SAAS;gBAAEC,EAAE,EAAE;cAAO,CAAC;cACxC,SAAS,EAAE;gBACTQ,UAAU,EAAE,mDAAmD;gBAC/DE,SAAS,EAAE,kBAAkB;gBAC7BC,SAAS,EAAE;cACb,CAAC;cACD,YAAY,EAAE;gBACZH,UAAU,EAAE,SAAS;gBACrBvC,KAAK,EAAE;cACT,CAAC;cACD2C,UAAU,EAAE;YACd,CAAE;YAAAlB,QAAA,EAEDtJ,OAAO,GAAG,gBAAgB,GAAG;UAAa;YAAA+H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC,gBAET1I,OAAA,CAAC1B,MAAM;YACLiM,OAAO,EAAC,WAAW;YACnBE,SAAS,EAAEjK,OAAO,gBAAGR,OAAA,CAACtB,gBAAgB;cAACuJ,IAAI,EAAE,EAAG;cAACC,EAAE,EAAE;gBAAEG,KAAK,EAAE;cAAU;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAG1I,OAAA,CAAChC,YAAY;cAAAuK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACjGgC,OAAO,EAAEA,CAAA,KAAMrE,cAAc,CAACvF,iBAAiB,CAAE;YACjD6J,QAAQ,EAAEnK,OAAQ;YAClB0H,EAAE,EAAE;cACF0C,UAAU,EAAE,mDAAmD;cAC/DC,SAAS,EAAE;gBAAEV,EAAE,EAAE,SAAS;gBAAEC,EAAE,EAAE;cAAO,CAAC;cACxC,SAAS,EAAE;gBACTQ,UAAU,EAAE,mDAAmD;gBAC/DE,SAAS,EAAE,kBAAkB;gBAC7BC,SAAS,EAAE;cACb,CAAC;cACD,YAAY,EAAE;gBACZH,UAAU,EAAE,SAAS;gBACrBvC,KAAK,EAAE;cACT,CAAC;cACD2C,UAAU,EAAE;YACd,CAAE;YAAAlB,QAAA,EAEDtJ,OAAO,GAAG,iBAAiB,GAAG;UAAc;YAAA+H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC;QACT,gBACD,CACH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAELhI,aAAa,iBACZV,OAAA,CAACjB,KAAK;QAACmJ,EAAE,EAAE;UAAE6B,CAAC,EAAE,CAAC;UAAEF,EAAE,EAAE,CAAC;UAAEoB,OAAO,EAAE;QAAc,CAAE;QAAAnB,QAAA,eACjD9J,OAAA,CAACT,UAAU;UAAC8I,KAAK,EAAC,OAAO;UAAAyB,QAAA,EAAEpJ;QAAa;UAAA6H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CACR,EAEA,CAACrI,OAAO,IAAIC,SAAS,CAAC4K,IAAI,CAACjJ,GAAG,IAAIA,GAAG,CAACf,MAAM,KAAK,YAAY,CAAC,iBAC7DlB,OAAA,CAACzB,IAAI;QAAC2J,EAAE,EAAE;UAAE2B,EAAE,EAAE,CAAC;UAAEoB,OAAO,EAAE;QAAgB,CAAE;QAAAnB,QAAA,eAC5C9J,OAAA,CAACxB,WAAW;UAAAsL,QAAA,gBACV9J,OAAA,CAACT,UAAU;YAACgL,OAAO,EAAC,IAAI;YAACY,YAAY;YAAArB,QAAA,EAAC;UAEtC;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb1I,OAAA,CAACT,UAAU;YAACgL,OAAO,EAAC,OAAO;YAAAT,QAAA,EAAC;UAE5B;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CACP,EAGArI,OAAO,iBACNL,OAAA,CAAC3B,GAAG;QAAC6J,EAAE,EAAE;UAAE2B,EAAE,EAAE,CAAC;UAAEE,CAAC,EAAE,CAAC;UAAEkB,OAAO,EAAE,SAAS;UAAEG,YAAY,EAAE;QAAE,CAAE;QAAAtB,QAAA,gBAC5D9J,OAAA,CAACT,UAAU;UAACgL,OAAO,EAAC,IAAI;UAACrC,EAAE,EAAE;YAAE2B,EAAE,EAAE,CAAC;YAAE1B,UAAU,EAAE;UAAI,CAAE;UAAA2B,QAAA,EAAC;QAEzD;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb1I,OAAA,CAACpB,IAAI;UAACyM,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAxB,QAAA,gBACzB9J,OAAA,CAACpB,IAAI;YAAC2M,IAAI;YAACpB,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACoB,EAAE,EAAE,CAAE;YAAA1B,QAAA,eAC9B9J,OAAA,CAACV,SAAS;cACRmM,MAAM;cACNC,SAAS;cACT1D,KAAK,EAAC,QAAQ;cACdwB,KAAK,EAAExI,OAAO,CAACE,MAAO;cACtByK,QAAQ,EAAGC,CAAC,IAAKtC,kBAAkB,CAAC,QAAQ,EAAEsC,CAAC,CAACC,MAAM,CAACrC,KAAK,CAAE;cAC9DvB,IAAI,EAAC,OAAO;cAAA6B,QAAA,gBAEZ9J,OAAA,CAAClB,QAAQ;gBAAC0K,KAAK,EAAC,KAAK;gBAAAM,QAAA,EAAC;cAAU;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAC3C1I,OAAA,CAAClB,QAAQ;gBAAC0K,KAAK,EAAC,YAAY;gBAAAM,QAAA,EAAC;cAAU;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAClD1I,OAAA,CAAClB,QAAQ;gBAAC0K,KAAK,EAAC,aAAa;gBAAAM,QAAA,EAAC;cAAW;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eACP1I,OAAA,CAACpB,IAAI;YAAC2M,IAAI;YAACpB,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACoB,EAAE,EAAE,CAAE;YAAA1B,QAAA,eAC9B9J,OAAA,CAACV,SAAS;cACRmM,MAAM;cACNC,SAAS;cACT1D,KAAK,EAAC,oBAAoB;cAC1BwB,KAAK,EAAExI,OAAO,CAACG,QAAS;cACxBwK,QAAQ,EAAGC,CAAC,IAAKtC,kBAAkB,CAAC,UAAU,EAAEsC,CAAC,CAACC,MAAM,CAACrC,KAAK,CAAE;cAChEvB,IAAI,EAAC,OAAO;cACZC,EAAE,EAAE;gBACF,0BAA0B,EAAE;kBAC1BkD,YAAY,EAAE,CAAC;kBACfhD,eAAe,EAAE,SAAS;kBAC1B,kBAAkB,EAAE;oBAClB0D,WAAW,EAAE;kBACf,CAAC;kBACD,wBAAwB,EAAE;oBACxBA,WAAW,EAAE,SAAS;oBACtBC,WAAW,EAAE;kBACf;gBACF,CAAC;gBACD,uBAAuB,EAAE;kBACvB,eAAe,EAAE;oBACf1D,KAAK,EAAE,SAAS;oBAChBF,UAAU,EAAE;kBACd;gBACF;cACF,CAAE;cAAA2B,QAAA,gBAEF9J,OAAA,CAAClB,QAAQ;gBAAC0K,KAAK,EAAC,KAAK;gBAAAM,QAAA,EAAC;cAAa;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,EAC7ClH,SAAS,CAACQ,GAAG,CAAEb,QAAQ,iBACtBnB,OAAA,CAAClB,QAAQ;gBAAmB0K,KAAK,EAAErI,QAAQ,CAAC6B,IAAK;gBAAA8G,QAAA,eAC/C9J,OAAA,CAAC3B,GAAG;kBAAC6J,EAAE,EAAE;oBAAE8B,OAAO,EAAE,MAAM;oBAAEK,aAAa,EAAE;kBAAS,CAAE;kBAAAP,QAAA,gBACpD9J,OAAA,CAACT,UAAU;oBAACgL,OAAO,EAAC,OAAO;oBAACrC,EAAE,EAAE;sBAAEC,UAAU,EAAE;oBAAI,CAAE;oBAAA2B,QAAA,EACjD3I,QAAQ,CAAC6B;kBAAI;oBAAAuF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACb1I,OAAA,CAACT,UAAU;oBAACgL,OAAO,EAAC,SAAS;oBAAClC,KAAK,EAAC,gBAAgB;oBAAAyB,QAAA,GAAC,MAC/C,EAAC3I,QAAQ,CAAC8B,UAAU;kBAAA;oBAAAsF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACd,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC,GAROvH,QAAQ,CAACkB,EAAE;gBAAAkG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAShB,CACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eACP1I,OAAA,CAACpB,IAAI;YAAC2M,IAAI;YAACpB,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACoB,EAAE,EAAE,CAAE;YAAA1B,QAAA,eAC9B9J,OAAA,CAACV,SAAS;cACRmM,MAAM;cACNC,SAAS;cACT1D,KAAK,EAAC,SAAS;cACfwB,KAAK,EAAEpI,MAAO;cACduK,QAAQ,EAAGC,CAAC,IAAKvK,SAAS,CAACuK,CAAC,CAACC,MAAM,CAACrC,KAAK,CAAE;cAC3CvB,IAAI,EAAC,OAAO;cAAA6B,QAAA,gBAEZ9J,OAAA,CAAClB,QAAQ;gBAAC0K,KAAK,EAAC,aAAa;gBAAAM,QAAA,EAAC;cAAa;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACtD1I,OAAA,CAAClB,QAAQ;gBAAC0K,KAAK,EAAC,cAAc;gBAAAM,QAAA,EAAC;cAAc;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACxD1I,OAAA,CAAClB,QAAQ;gBAAC0K,KAAK,EAAC,cAAc;gBAAAM,QAAA,EAAC;cAAa;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACvD1I,OAAA,CAAClB,QAAQ;gBAAC0K,KAAK,EAAC,QAAQ;gBAAAM,QAAA,EAAC;cAAM;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACP1I,OAAA,CAAC3B,GAAG;UAAC6J,EAAE,EAAE;YAAE0B,EAAE,EAAE,CAAC;YAAEI,OAAO,EAAE,MAAM;YAAEM,GAAG,EAAE;UAAE,CAAE;UAAAR,QAAA,gBAC1C9J,OAAA,CAAC1B,MAAM;YACLiM,OAAO,EAAC,UAAU;YAClBtC,IAAI,EAAC,OAAO;YACZyC,OAAO,EAAEA,CAAA,KAAMnJ,YAAY,CAACD,SAAS,KAAK,KAAK,GAAG,MAAM,GAAG,KAAK,CAAE;YAAAwI,QAAA,EAEjExI,SAAS,KAAK,KAAK,GAAG,aAAa,GAAG;UAAc;YAAAiH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC,eACT1I,OAAA,CAAC1B,MAAM;YACLiM,OAAO,EAAC,UAAU;YAClBtC,IAAI,EAAC,OAAO;YACZyC,OAAO,EAAEA,CAAA,KAAM;cACbzJ,UAAU,CAAC;gBAAEC,MAAM,EAAE,KAAK;gBAAEC,QAAQ,EAAE;cAAM,CAAC,CAAC;cAC9CE,SAAS,CAAC,aAAa,CAAC;cACxBE,YAAY,CAAC,MAAM,CAAC;YACtB,CAAE;YAAAuI,QAAA,EACH;UAED;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAED1I,OAAA,CAACb,cAAc;QACbqL,SAAS,EAAEzL,KAAM;QACjBwL,OAAO,EAAC,UAAU;QAClBrC,EAAE,EAAE;UACF8D,SAAS,EAAE,MAAM;UACjB,kBAAkB,EAAE;YAClBC,QAAQ,EAAE;cAAE9B,EAAE,EAAE,GAAG;cAAEC,EAAE,EAAE;YAAI;UAC/B;QACF,CAAE;QAAAN,QAAA,eAEF9J,OAAA,CAAChB,KAAK;UAAA8K,QAAA,gBACJ9J,OAAA,CAACZ,SAAS;YAAA0K,QAAA,eACR9J,OAAA,CAACX,QAAQ;cAAAyK,QAAA,GACNzJ,OAAO,iBAAIL,OAAA,CAACd,SAAS;gBAAA4K,QAAA,EAAC;cAAQ;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,EAC1CrI,OAAO,iBAAIL,OAAA,CAACd,SAAS;gBAAA4K,QAAA,EAAC;cAAQ;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC3C1I,OAAA,CAACd,SAAS;gBAAA4K,QAAA,EAAC;cAAa;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACpC1I,OAAA,CAACd,SAAS;gBAAA4K,QAAA,EAAC;cAAc;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACrC1I,OAAA,CAACd,SAAS;gBAAA4K,QAAA,EAAC;cAAM;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC7B1I,OAAA,CAACd,SAAS;gBAACgN,KAAK,EAAC,OAAO;gBAAApC,QAAA,EAAC;cAAO;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZ1I,OAAA,CAACf,SAAS;YAAA6K,QAAA,EACPJ,iBAAiB,CAACyC,MAAM,KAAK,CAAC,gBAC7BnM,OAAA,CAACX,QAAQ;cAAAyK,QAAA,eACP9J,OAAA,CAACd,SAAS;gBAACkN,OAAO,EAAE/L,OAAO,GAAG,CAAC,GAAG,CAAE;gBAAC6L,KAAK,EAAC,QAAQ;gBAAApC,QAAA,EAChDzJ,OAAO,KAAKW,OAAO,CAACE,MAAM,KAAK,KAAK,IAAIF,OAAO,CAACG,QAAQ,KAAK,KAAK,CAAC,GAChE,wCAAwC,GACxC;cAA4B;gBAAAoH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,GAEXgB,iBAAiB,CAAC1H,GAAG,CAAEC,GAAG,iBACxBjC,OAAA,CAACX,QAAQ;cAAAyK,QAAA,GACNzJ,OAAO,iBACNL,OAAA,CAACd,SAAS;gBAAA4K,QAAA,eACR9J,OAAA,CAAC3B,GAAG;kBAAAyL,QAAA,gBACF9J,OAAA,CAACT,UAAU;oBAACgL,OAAO,EAAC,OAAO;oBAACrC,EAAE,EAAE;sBAAEC,UAAU,EAAE;oBAAI,CAAE;oBAAA2B,QAAA,EACjD7H,GAAG,CAACa;kBAAY;oBAAAyF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC,eACb1I,OAAA,CAACT,UAAU;oBAACgL,OAAO,EAAC,SAAS;oBAACrC,EAAE,EAAE;sBAAEG,KAAK,EAAE;oBAAU,CAAE;oBAAAyB,QAAA,GAAC,MAClD,EAAC7H,GAAG,CAACgB,UAAU;kBAAA;oBAAAsF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CACZ,EACArI,OAAO,iBACNL,OAAA,CAACd,SAAS;gBAAA4K,QAAA,eACR9J,OAAA,CAAC3B,GAAG;kBAAC6J,EAAE,EAAE;oBAAE8B,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE,QAAQ;oBAAEI,GAAG,EAAE;kBAAE,CAAE;kBAAAR,QAAA,gBACzD9J,OAAA,CAACT,UAAU;oBAACgL,OAAO,EAAC,OAAO;oBAACrC,EAAE,EAAE;sBAAEmE,IAAI,EAAE;oBAAE,CAAE;oBAAAvC,QAAA,EACzC7H,GAAG,CAACQ;kBAAO;oBAAA8F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,EAEZzG,GAAG,CAACf,MAAM,KAAK,YAAY,iBAC1BlB,OAAA,CAACnB,UAAU;oBACTwJ,KAAK,EAAC,SAAS;oBACfqC,OAAO,EAAEA,CAAA,KAAMrD,sBAAsB,CAACpF,GAAG,CAAE;oBAC3CgG,IAAI,EAAC,OAAO;oBACZC,EAAE,EAAE;sBACFE,eAAe,EAAE,yBAAyB;sBAC1C,SAAS,EAAE;wBACTA,eAAe,EAAE,yBAAyB;wBAC1C0C,SAAS,EAAE;sBACb,CAAC;sBACDE,UAAU,EAAE;oBACd,CAAE;oBACFsB,KAAK,EAAC,mCAAmC;oBAAAxC,QAAA,eAEzC9J,OAAA,CAAC5B,YAAY;sBAAC8J,EAAE,EAAE;wBAAEG,KAAK,EAAE;sBAAU;oBAAE;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC,CACb;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CACZ,eACD1I,OAAA,CAACd,SAAS;gBAAA4K,QAAA,EAAE7H,GAAG,CAACS;cAAW;gBAAA6F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACxC1I,OAAA,CAACd,SAAS;gBAAA4K,QAAA,EAAE7H,GAAG,CAACY,YAAY,IAAI;cAAqB;gBAAA0F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAClE1I,OAAA,CAACd,SAAS;gBAAA4K,QAAA,EAAE/B,aAAa,CAAC9F,GAAG,CAACf,MAAM;cAAC;gBAAAqH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAClD1I,OAAA,CAACd,SAAS;gBAACgN,KAAK,EAAC,OAAO;gBAAApC,QAAA,GAErB,CAACzJ,OAAO,IAAI4B,GAAG,CAACf,MAAM,KAAK,YAAY,iBACtClB,OAAA,CAACnB,UAAU;kBACTwJ,KAAK,EAAC,SAAS;kBACfqC,OAAO,EAAEA,CAAA,KAAMrE,cAAc,CAACpE,GAAG,CAACI,EAAE,CAAE;kBACtC4F,IAAI,EAAC,OAAO;kBACZC,EAAE,EAAE;oBACF,SAAS,EAAE;sBACTE,eAAe,EAAE;oBACnB;kBACF,CAAE;kBAAA0B,QAAA,eAEF9J,OAAA,CAAChC,YAAY;oBAAAuK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CACb,EAGArI,OAAO,iBACNL,OAAA,CAACnB,UAAU;kBACTwJ,KAAK,EAAC,OAAO;kBACbqC,OAAO,EAAEA,CAAA,KAAMvD,oBAAoB,CAAClF,GAAG,CAACI,EAAE,CAAE;kBAC5C4F,IAAI,EAAC,OAAO;kBACZC,EAAE,EAAE;oBACF,SAAS,EAAE;sBACTE,eAAe,EAAE;oBACnB;kBACF,CAAE;kBACFkE,KAAK,EAAC,wBAAwB;kBAAAxC,QAAA,eAE9B9J,OAAA,CAAC9B,UAAU;oBAAAqK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CACb;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC;YAAA,GA7ECzG,GAAG,CAACI,EAAE;cAAAkG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA8EX,CACX;UACF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAGC,CAAC;AAEhB,CAAC;AAACtI,EAAA,CAntBID,SAAS;AAAAoM,EAAA,GAATpM,SAAS;AAqtBf,eAAeA,SAAS;AAAC,IAAAoM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}