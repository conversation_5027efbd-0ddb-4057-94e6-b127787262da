[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\reportWebVitals.js": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\App.js": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\Dashboard.js": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\Employees.js": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\Profile.js": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\Documents.js": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\NotFound.js": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\Unauthorized.js": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\Locations.js": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\context\\AuthContext.js": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\components\\common\\ProtectedRoute.js": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\components\\auth\\Login.js": "13", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\components\\layout\\Navbar.js": "14", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\services\\api.js": "15", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\components\\layout\\AdminLayout.js": "16"}, {"size": 535, "mtime": 1747834996172, "results": "17", "hashOfConfig": "18"}, {"size": 362, "mtime": 1747834996587, "results": "19", "hashOfConfig": "18"}, {"size": 4553, "mtime": 1748185336043, "results": "20", "hashOfConfig": "18"}, {"size": 16123, "mtime": 1748252411720, "results": "21", "hashOfConfig": "18"}, {"size": 23613, "mtime": 1748200730024, "results": "22", "hashOfConfig": "18"}, {"size": 6376, "mtime": 1748196326737, "results": "23", "hashOfConfig": "18"}, {"size": 37045, "mtime": 1748200275718, "results": "24", "hashOfConfig": "18"}, {"size": 988, "mtime": 1747836738112, "results": "25", "hashOfConfig": "18"}, {"size": 1015, "mtime": 1747836723734, "results": "26", "hashOfConfig": "18"}, {"size": 26683, "mtime": 1748252060009, "results": "27", "hashOfConfig": "18"}, {"size": 3312, "mtime": 1748171905541, "results": "28", "hashOfConfig": "18"}, {"size": 757, "mtime": 1747835314254, "results": "29", "hashOfConfig": "18"}, {"size": 11706, "mtime": 1748252458363, "results": "30", "hashOfConfig": "18"}, {"size": 6246, "mtime": 1748182139800, "results": "31", "hashOfConfig": "18"}, {"size": 2686, "mtime": 1748199814520, "results": "32", "hashOfConfig": "18"}, {"size": 15499, "mtime": 1748251978099, "results": "33", "hashOfConfig": "18"}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1w8hjfx", {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\Dashboard.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\Employees.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\Profile.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\Documents.js", ["82"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\NotFound.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\Unauthorized.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\Locations.js", ["83", "84", "85"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\context\\AuthContext.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\components\\common\\ProtectedRoute.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\components\\auth\\Login.js", ["86"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\components\\layout\\Navbar.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\services\\api.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\components\\layout\\AdminLayout.js", [], [], {"ruleId": "87", "severity": 1, "message": "88", "line": 476, "column": 9, "nodeType": "89", "messageId": "90", "endLine": 476, "endColumn": 25}, {"ruleId": "91", "severity": 1, "message": "92", "line": 107, "column": 6, "nodeType": "93", "endLine": 107, "endColumn": 15, "suggestions": "94"}, {"ruleId": "87", "severity": 1, "message": "95", "line": 286, "column": 10, "nodeType": "89", "messageId": "90", "endLine": 286, "endColumn": 26}, {"ruleId": "91", "severity": 1, "message": "96", "line": 341, "column": 6, "nodeType": "93", "endLine": 341, "endColumn": 15, "suggestions": "97"}, {"ruleId": "87", "severity": 1, "message": "98", "line": 42, "column": 10, "nodeType": "89", "messageId": "90", "endLine": 42, "endColumn": 17}, "no-unused-vars", "'handleSortChange' is assigned a value but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchLocations'. Either include it or remove the dependency array.", "ArrayExpression", ["99"], "'activeLocationId' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'stopLiveLocationTracking'. Either include it or remove the dependency array.", ["100"], "'mounted' is assigned a value but never used.", {"desc": "101", "fix": "102"}, {"desc": "103", "fix": "104"}, "Update the dependencies array to be: [fetchLocations, isAdmin]", {"range": "105", "text": "106"}, "Update the dependencies array to be: [stopLiveLocationTracking, watchId]", {"range": "107", "text": "108"}, [3478, 3487], "[fetchLocations, isAdmin]", [11008, 11017], "[stopLiveLocationTracking, watchId]"]