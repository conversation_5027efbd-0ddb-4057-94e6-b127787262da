[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\reportWebVitals.js": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\App.js": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\Dashboard.js": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\Employees.js": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\Profile.js": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\Documents.js": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\NotFound.js": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\Unauthorized.js": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\Locations.js": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\context\\AuthContext.js": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\components\\common\\ProtectedRoute.js": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\components\\auth\\Login.js": "13", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\components\\layout\\Navbar.js": "14", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\services\\api.js": "15", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\components\\layout\\AdminLayout.js": "16", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\HelpCenter.js": "17", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\PaymentRecords.js": "18", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\components\\PaymentRecordDialogs.js": "19", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\EmployeePayments.js": "20"}, {"size": 535, "mtime": 1747834996172, "results": "21", "hashOfConfig": "22"}, {"size": 362, "mtime": 1747834996587, "results": "23", "hashOfConfig": "22"}, {"size": 5010, "mtime": 1748529605039, "results": "24", "hashOfConfig": "22"}, {"size": 11906, "mtime": 1748252880586, "results": "25", "hashOfConfig": "22"}, {"size": 23613, "mtime": 1748200730024, "results": "26", "hashOfConfig": "22"}, {"size": 6376, "mtime": 1748196326737, "results": "27", "hashOfConfig": "22"}, {"size": 37045, "mtime": 1748200275718, "results": "28", "hashOfConfig": "22"}, {"size": 988, "mtime": 1747836738112, "results": "29", "hashOfConfig": "22"}, {"size": 1015, "mtime": 1747836723734, "results": "30", "hashOfConfig": "22"}, {"size": 26683, "mtime": 1748252060009, "results": "31", "hashOfConfig": "22"}, {"size": 3312, "mtime": 1748171905541, "results": "32", "hashOfConfig": "22"}, {"size": 757, "mtime": 1747835314254, "results": "33", "hashOfConfig": "22"}, {"size": 11706, "mtime": 1748252998687, "results": "34", "hashOfConfig": "22"}, {"size": 6246, "mtime": 1748182139800, "results": "35", "hashOfConfig": "22"}, {"size": 3756, "mtime": 1748587585984, "results": "36", "hashOfConfig": "22"}, {"size": 17010, "mtime": 1748588438727, "results": "37", "hashOfConfig": "22"}, {"size": 24082, "mtime": 1748335556141, "results": "38", "hashOfConfig": "22"}, {"size": 18785, "mtime": 1748589148477, "results": "39", "hashOfConfig": "22"}, {"size": 21982, "mtime": 1748590255511, "results": "40", "hashOfConfig": "22"}, {"size": 14695, "mtime": 1748587993370, "results": "41", "hashOfConfig": "22"}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1w8hjfx", {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 10, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\Dashboard.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\Employees.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\Profile.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\Documents.js", ["102"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\NotFound.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\Unauthorized.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\Locations.js", ["103", "104", "105"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\context\\AuthContext.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\components\\common\\ProtectedRoute.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\components\\auth\\Login.js", ["106"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\components\\layout\\Navbar.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\services\\api.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\components\\layout\\AdminLayout.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\HelpCenter.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\PaymentRecords.js", ["107", "108"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\components\\PaymentRecordDialogs.js", ["109", "110", "111", "112", "113", "114", "115", "116", "117", "118"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\EmployeePayments.js", ["119"], [], {"ruleId": "120", "severity": 1, "message": "121", "line": 476, "column": 9, "nodeType": "122", "messageId": "123", "endLine": 476, "endColumn": 25}, {"ruleId": "124", "severity": 1, "message": "125", "line": 107, "column": 6, "nodeType": "126", "endLine": 107, "endColumn": 15, "suggestions": "127"}, {"ruleId": "120", "severity": 1, "message": "128", "line": 286, "column": 10, "nodeType": "122", "messageId": "123", "endLine": 286, "endColumn": 26}, {"ruleId": "124", "severity": 1, "message": "129", "line": 341, "column": 6, "nodeType": "126", "endLine": 341, "endColumn": 15, "suggestions": "130"}, {"ruleId": "120", "severity": 1, "message": "131", "line": 42, "column": 10, "nodeType": "122", "messageId": "123", "endLine": 42, "endColumn": 17}, {"ruleId": "124", "severity": 1, "message": "132", "line": 149, "column": 6, "nodeType": "126", "endLine": 149, "endColumn": 25, "suggestions": "133"}, {"ruleId": "124", "severity": 1, "message": "132", "line": 192, "column": 6, "nodeType": "126", "endLine": 192, "endColumn": 41, "suggestions": "134"}, {"ruleId": "135", "severity": 2, "message": "136", "line": 52, "column": 51, "nodeType": "122", "messageId": "137", "endLine": 52, "endColumn": 59}, {"ruleId": "135", "severity": 2, "message": "138", "line": 55, "column": 3, "nodeType": "122", "messageId": "137", "endLine": 55, "endColumn": 12}, {"ruleId": "135", "severity": 2, "message": "139", "line": 60, "column": 32, "nodeType": "122", "messageId": "137", "endLine": 60, "endColumn": 43}, {"ruleId": "135", "severity": 2, "message": "139", "line": 65, "column": 30, "nodeType": "122", "messageId": "137", "endLine": 65, "endColumn": 41}, {"ruleId": "135", "severity": 2, "message": "139", "line": 88, "column": 20, "nodeType": "122", "messageId": "137", "endLine": 88, "endColumn": 31}, {"ruleId": "135", "severity": 2, "message": "139", "line": 97, "column": 23, "nodeType": "122", "messageId": "137", "endLine": 97, "endColumn": 34}, {"ruleId": "135", "severity": 2, "message": "139", "line": 105, "column": 23, "nodeType": "122", "messageId": "137", "endLine": 105, "endColumn": 34}, {"ruleId": "135", "severity": 2, "message": "139", "line": 114, "column": 24, "nodeType": "122", "messageId": "137", "endLine": 114, "endColumn": 35}, {"ruleId": "135", "severity": 2, "message": "139", "line": 123, "column": 27, "nodeType": "122", "messageId": "137", "endLine": 123, "endColumn": 38}, {"ruleId": "135", "severity": 2, "message": "139", "line": 131, "column": 27, "nodeType": "122", "messageId": "137", "endLine": 131, "endColumn": 38}, {"ruleId": "120", "severity": 1, "message": "140", "line": 36, "column": 11, "nodeType": "122", "messageId": "123", "endLine": 36, "endColumn": 15}, "no-unused-vars", "'handleSortChange' is assigned a value but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchLocations'. Either include it or remove the dependency array.", "ArrayExpression", ["141"], "'activeLocationId' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'stopLiveLocationTracking'. Either include it or remove the dependency array.", ["142"], "'mounted' is assigned a value but never used.", "React Hook useCallback has a missing dependency: 'resetForm'. Either include it or remove the dependency array.", ["143"], ["144"], "no-undef", "'useState' is not defined.", "undef", "'useEffect' is not defined.", "'useCallback' is not defined.", "'user' is assigned a value but never used.", {"desc": "145", "fix": "146"}, {"desc": "147", "fix": "148"}, {"desc": "149", "fix": "150"}, {"desc": "151", "fix": "152"}, "Update the dependencies array to be: [fetchLocations, isAdmin]", {"range": "153", "text": "154"}, "Update the dependencies array to be: [stopLiveLocationTracking, watchId]", {"range": "155", "text": "156"}, "Update the dependencies array to be: [formData, resetForm, filters]", {"range": "157", "text": "158"}, "Update the dependencies array to be: [formData.basicSalary, formData.overtime.hours, formData.overtime.rate, formData.bonuses, formData.deductions, formData.paymentMethod, formData.notes, selectedRecord._id, resetForm, filters]", {"range": "159", "text": "160"}, [3478, 3487], "[fetchLocations, isAdmin]", [11008, 11017], "[stopLiveLocationTracking, watchId]", [4500, 4519], "[formData, resetForm, filters]", [6000, 6035], "[formData.basicSalary, formData.overtime.hours, formData.overtime.rate, formData.bonuses, formData.deductions, formData.paymentMethod, formData.notes, selectedRecord._id, resetForm, filters]"]