[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\reportWebVitals.js": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\App.js": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\Dashboard.js": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\Employees.js": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\Profile.js": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\Documents.js": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\NotFound.js": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\Unauthorized.js": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\Locations.js": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\context\\AuthContext.js": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\components\\common\\ProtectedRoute.js": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\components\\auth\\Login.js": "13", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\components\\layout\\Navbar.js": "14", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\services\\api.js": "15", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\components\\layout\\AdminLayout.js": "16", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\HelpCenter.js": "17"}, {"size": 535, "mtime": 1747834996172, "results": "18", "hashOfConfig": "19"}, {"size": 362, "mtime": 1747834996587, "results": "20", "hashOfConfig": "19"}, {"size": 4798, "mtime": 1748521866484, "results": "21", "hashOfConfig": "19"}, {"size": 11906, "mtime": 1748252880586, "results": "22", "hashOfConfig": "19"}, {"size": 23613, "mtime": 1748200730024, "results": "23", "hashOfConfig": "19"}, {"size": 6376, "mtime": 1748196326737, "results": "24", "hashOfConfig": "19"}, {"size": 37045, "mtime": 1748200275718, "results": "25", "hashOfConfig": "19"}, {"size": 988, "mtime": 1747836738112, "results": "26", "hashOfConfig": "19"}, {"size": 1015, "mtime": 1747836723734, "results": "27", "hashOfConfig": "19"}, {"size": 26683, "mtime": 1748252060009, "results": "28", "hashOfConfig": "19"}, {"size": 3312, "mtime": 1748171905541, "results": "29", "hashOfConfig": "19"}, {"size": 757, "mtime": 1747835314254, "results": "30", "hashOfConfig": "19"}, {"size": 11706, "mtime": 1748252998687, "results": "31", "hashOfConfig": "19"}, {"size": 6246, "mtime": 1748182139800, "results": "32", "hashOfConfig": "19"}, {"size": 3687, "mtime": 1748521441377, "results": "33", "hashOfConfig": "19"}, {"size": 15788, "mtime": 1748521990615, "results": "34", "hashOfConfig": "19"}, {"size": 24082, "mtime": 1748335556141, "results": "35", "hashOfConfig": "19"}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1w8hjfx", {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\App.js", ["87"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\Dashboard.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\Employees.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\Profile.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\Documents.js", ["88"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\NotFound.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\Unauthorized.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\Locations.js", ["89", "90", "91"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\context\\AuthContext.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\components\\common\\ProtectedRoute.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\components\\auth\\Login.js", ["92"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\components\\layout\\Navbar.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\services\\api.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\components\\layout\\AdminLayout.js", ["93"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\HelpCenter.js", [], [], {"ruleId": "94", "severity": 2, "message": "95", "line": 160, "column": 69, "nodeType": "96", "messageId": "97", "endLine": 160, "endColumn": 83}, {"ruleId": "98", "severity": 1, "message": "99", "line": 476, "column": 9, "nodeType": "100", "messageId": "101", "endLine": 476, "endColumn": 25}, {"ruleId": "102", "severity": 1, "message": "103", "line": 107, "column": 6, "nodeType": "104", "endLine": 107, "endColumn": 15, "suggestions": "105"}, {"ruleId": "98", "severity": 1, "message": "106", "line": 286, "column": 10, "nodeType": "100", "messageId": "101", "endLine": 286, "endColumn": 26}, {"ruleId": "102", "severity": 1, "message": "107", "line": 341, "column": 6, "nodeType": "104", "endLine": 341, "endColumn": 15, "suggestions": "108"}, {"ruleId": "98", "severity": 1, "message": "109", "line": 42, "column": 10, "nodeType": "100", "messageId": "101", "endLine": 42, "endColumn": 17}, {"ruleId": "94", "severity": 2, "message": "110", "line": 111, "column": 14, "nodeType": "96", "messageId": "97", "endLine": 111, "endColumn": 25}, "react/jsx-no-undef", "'PaymentRecords' is not defined.", "JSXIdentifier", "undefined", "no-unused-vars", "'handleSortChange' is assigned a value but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchLocations'. Either include it or remove the dependency array.", "ArrayExpression", ["111"], "'activeLocationId' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'stopLiveLocationTracking'. Either include it or remove the dependency array.", ["112"], "'mounted' is assigned a value but never used.", "'PaymentIcon' is not defined.", {"desc": "113", "fix": "114"}, {"desc": "115", "fix": "116"}, "Update the dependencies array to be: [fetchLocations, isAdmin]", {"range": "117", "text": "118"}, "Update the dependencies array to be: [stopLiveLocationTracking, watchId]", {"range": "119", "text": "120"}, [3478, 3487], "[fetchLocations, isAdmin]", [11008, 11017], "[stopLiveLocationTracking, watchId]"]