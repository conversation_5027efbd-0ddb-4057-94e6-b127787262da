[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\reportWebVitals.js": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\App.js": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\Dashboard.js": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\Employees.js": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\Profile.js": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\Documents.js": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\NotFound.js": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\Unauthorized.js": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\Locations.js": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\context\\AuthContext.js": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\components\\common\\ProtectedRoute.js": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\components\\auth\\Login.js": "13", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\components\\layout\\Navbar.js": "14", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\services\\api.js": "15", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\components\\layout\\AdminLayout.js": "16", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\HelpCenter.js": "17", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\PaymentRecords.js": "18", "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\components\\PaymentRecordDialogs.js": "19"}, {"size": 535, "mtime": 1747834996172, "results": "20", "hashOfConfig": "21"}, {"size": 362, "mtime": 1747834996587, "results": "22", "hashOfConfig": "21"}, {"size": 4851, "mtime": 1748526511909, "results": "23", "hashOfConfig": "21"}, {"size": 11906, "mtime": 1748252880586, "results": "24", "hashOfConfig": "21"}, {"size": 23613, "mtime": 1748200730024, "results": "25", "hashOfConfig": "21"}, {"size": 6376, "mtime": 1748196326737, "results": "26", "hashOfConfig": "21"}, {"size": 37045, "mtime": 1748200275718, "results": "27", "hashOfConfig": "21"}, {"size": 988, "mtime": 1747836738112, "results": "28", "hashOfConfig": "21"}, {"size": 1015, "mtime": 1747836723734, "results": "29", "hashOfConfig": "21"}, {"size": 26683, "mtime": 1748252060009, "results": "30", "hashOfConfig": "21"}, {"size": 3312, "mtime": 1748171905541, "results": "31", "hashOfConfig": "21"}, {"size": 757, "mtime": 1747835314254, "results": "32", "hashOfConfig": "21"}, {"size": 11706, "mtime": 1748252998687, "results": "33", "hashOfConfig": "21"}, {"size": 6246, "mtime": 1748182139800, "results": "34", "hashOfConfig": "21"}, {"size": 3687, "mtime": 1748521441377, "results": "35", "hashOfConfig": "21"}, {"size": 15816, "mtime": 1748526552575, "results": "36", "hashOfConfig": "21"}, {"size": 24082, "mtime": 1748335556141, "results": "37", "hashOfConfig": "21"}, {"size": 19694, "mtime": 1748521775470, "results": "38", "hashOfConfig": "21"}, {"size": 18954, "mtime": 1748521665271, "results": "39", "hashOfConfig": "21"}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1w8hjfx", {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\Dashboard.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\Employees.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\Profile.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\Documents.js", ["97"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\NotFound.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\Unauthorized.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\Locations.js", ["98", "99", "100"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\context\\AuthContext.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\components\\common\\ProtectedRoute.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\components\\auth\\Login.js", ["101"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\components\\layout\\Navbar.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\services\\api.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\components\\layout\\AdminLayout.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\HelpCenter.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\pages\\PaymentRecords.js", ["102"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\EmployeeManagementSystem\\frontend\\src\\components\\PaymentRecordDialogs.js", [], [], {"ruleId": "103", "severity": 1, "message": "104", "line": 476, "column": 9, "nodeType": "105", "messageId": "106", "endLine": 476, "endColumn": 25}, {"ruleId": "107", "severity": 1, "message": "108", "line": 107, "column": 6, "nodeType": "109", "endLine": 107, "endColumn": 15, "suggestions": "110"}, {"ruleId": "103", "severity": 1, "message": "111", "line": 286, "column": 10, "nodeType": "105", "messageId": "106", "endLine": 286, "endColumn": 26}, {"ruleId": "107", "severity": 1, "message": "112", "line": 341, "column": 6, "nodeType": "109", "endLine": 341, "endColumn": 15, "suggestions": "113"}, {"ruleId": "103", "severity": 1, "message": "114", "line": 42, "column": 10, "nodeType": "105", "messageId": "106", "endLine": 42, "endColumn": 17}, {"ruleId": "103", "severity": 1, "message": "115", "line": 206, "column": 9, "nodeType": "105", "messageId": "106", "endLine": 206, "endColumn": 27}, "no-unused-vars", "'handleSortChange' is assigned a value but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchLocations'. Either include it or remove the dependency array.", "ArrayExpression", ["116"], "'activeLocationId' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'stopLiveLocationTracking'. Either include it or remove the dependency array.", ["117"], "'mounted' is assigned a value but never used.", "'handleStatusChange' is assigned a value but never used.", {"desc": "118", "fix": "119"}, {"desc": "120", "fix": "121"}, "Update the dependencies array to be: [fetchLocations, isAdmin]", {"range": "122", "text": "123"}, "Update the dependencies array to be: [stopLiveLocationTracking, watchId]", {"range": "124", "text": "125"}, [3478, 3487], "[fetchLocations, isAdmin]", [11008, 11017], "[stopLiveLocationTracking, watchId]"]