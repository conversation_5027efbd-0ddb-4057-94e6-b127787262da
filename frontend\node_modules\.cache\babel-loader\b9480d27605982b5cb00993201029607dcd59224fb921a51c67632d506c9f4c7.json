{"ast": null, "code": "import axios from 'axios';\nconst API_URL = process.env.REACT_APP_API_URL;\n\n// Create axios instance\nconst api = axios.create({\n  baseURL: API_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Add a request interceptor to add the auth token to every request\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers['Authorization'] = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// Add a response interceptor to handle token expiration\napi.interceptors.response.use(response => {\n  return response;\n}, error => {\n  if (error.response && error.response.status === 401) {\n    // Token expired or invalid, logout the user\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    window.location.href = '/login';\n  }\n  return Promise.reject(error);\n});\n\n// Auth services\nexport const authService = {\n  login: credentials => api.post('/auth/login', credentials),\n  register: userData => api.post('/auth/register', userData),\n  getProfile: () => api.get('/auth/me'),\n  logout: () => api.get('/auth/logout')\n};\n\n// Employee services\nexport const employeeService = {\n  getAllEmployees: () => api.get('/employees'),\n  getEmployee: id => api.get(`/employees/${id}`),\n  createEmployee: employeeData => api.post('/employees', employeeData),\n  updateEmployee: (id, employeeData) => api.put(`/employees/${id}`, employeeData),\n  deleteEmployee: id => api.delete(`/employees/${id}`),\n  resetEmployeePassword: (id, newPassword) => api.put(`/employees/${id}/reset-password`, {\n    newPassword\n  })\n};\n\n// Document services\nexport const documentService = {\n  getAllDocuments: () => api.get('/documents'),\n  getDocument: id => api.get(`/documents/${id}`),\n  uploadDocument: formData => api.post('/documents', formData, {\n    headers: {\n      'Content-Type': 'multipart/form-data'\n    }\n  }),\n  updateDocumentStatus: (id, status) => api.put(`/documents/${id}/verify`, {\n    status\n  }),\n  verifyDocument: (id, status) => api.put(`/documents/${id}/verify`, {\n    status\n  }),\n  deleteDocument: id => api.delete(`/documents/${id}`)\n};\n\n// Location services\nexport const locationService = {\n  getAllLocations: () => api.get('/locations'),\n  getLocation: id => api.get(`/locations/${id}`),\n  checkIn: locationData => api.post('/locations/checkin', locationData),\n  checkOut: id => api.put(`/locations/${id}/checkout`),\n  deleteLocation: id => api.delete(`/locations/${id}`),\n  updateLiveLocation: (id, locationData) => api.put(`/locations/${id}/live-update`, locationData)\n};\n\n// Help Center services\nexport const helpCenterService = {\n  getAllTickets: params => api.get('/help-center', {\n    params\n  }),\n  getTicket: id => api.get(`/help-center/${id}`),\n  createTicket: ticketData => api.post('/help-center', ticketData),\n  replyToTicket: (id, replyData) => api.put(`/help-center/${id}/reply`, replyData),\n  updateTicketStatus: (id, statusData) => api.put(`/help-center/${id}/status`, statusData),\n  deleteTicket: id => api.delete(`/help-center/${id}`),\n  getStats: () => api.get('/help-center/stats')\n};\nexport default api;", "map": {"version": 3, "names": ["axios", "API_URL", "process", "env", "REACT_APP_API_URL", "api", "create", "baseURL", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "error", "Promise", "reject", "response", "status", "removeItem", "window", "location", "href", "authService", "login", "credentials", "post", "register", "userData", "getProfile", "get", "logout", "employeeService", "getAllEmployees", "getEmployee", "id", "createEmployee", "employeeData", "updateEmployee", "put", "deleteEmployee", "delete", "resetEmployeePassword", "newPassword", "documentService", "getAllDocuments", "getDocument", "uploadDocument", "formData", "updateDocumentStatus", "verifyDocument", "deleteDocument", "locationService", "getAllLocations", "getLocation", "checkIn", "locationData", "checkOut", "deleteLocation", "updateLiveLocation", "helpCenterService", "getAllTickets", "params", "getTicket", "createTicket", "ticketData", "replyToTicket", "replyData", "updateTicketStatus", "statusData", "deleteTicket", "getStats"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/EmployeeManagementSystem/frontend/src/services/api.js"], "sourcesContent": ["import axios from 'axios';\n\nconst API_URL = process.env.REACT_APP_API_URL;\n\n// Create axios instance\nconst api = axios.create({\n  baseURL: API_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Add a request interceptor to add the auth token to every request\napi.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers['Authorization'] = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Add a response interceptor to handle token expiration\napi.interceptors.response.use(\n  (response) => {\n    return response;\n  },\n  (error) => {\n    if (error.response && error.response.status === 401) {\n      // Token expired or invalid, logout the user\n      localStorage.removeItem('token');\n      localStorage.removeItem('user');\n      window.location.href = '/login';\n    }\n    return Promise.reject(error);\n  }\n);\n\n// Auth services\nexport const authService = {\n  login: (credentials) => api.post('/auth/login', credentials),\n  register: (userData) => api.post('/auth/register', userData),\n  getProfile: () => api.get('/auth/me'),\n  logout: () => api.get('/auth/logout')\n};\n\n// Employee services\nexport const employeeService = {\n  getAllEmployees: () => api.get('/employees'),\n  getEmployee: (id) => api.get(`/employees/${id}`),\n  createEmployee: (employeeData) => api.post('/employees', employeeData),\n  updateEmployee: (id, employeeData) => api.put(`/employees/${id}`, employeeData),\n  deleteEmployee: (id) => api.delete(`/employees/${id}`),\n  resetEmployeePassword: (id, newPassword) => api.put(`/employees/${id}/reset-password`, { newPassword })\n};\n\n// Document services\nexport const documentService = {\n  getAllDocuments: () => api.get('/documents'),\n  getDocument: (id) => api.get(`/documents/${id}`),\n  uploadDocument: (formData) => api.post('/documents', formData, {\n    headers: {\n      'Content-Type': 'multipart/form-data'\n    }\n  }),\n  updateDocumentStatus: (id, status) => api.put(`/documents/${id}/verify`, { status }),\n  verifyDocument: (id, status) => api.put(`/documents/${id}/verify`, { status }),\n  deleteDocument: (id) => api.delete(`/documents/${id}`)\n};\n\n// Location services\nexport const locationService = {\n  getAllLocations: () => api.get('/locations'),\n  getLocation: (id) => api.get(`/locations/${id}`),\n  checkIn: (locationData) => api.post('/locations/checkin', locationData),\n  checkOut: (id) => api.put(`/locations/${id}/checkout`),\n  deleteLocation: (id) => api.delete(`/locations/${id}`),\n  updateLiveLocation: (id, locationData) => api.put(`/locations/${id}/live-update`, locationData)\n};\n\n// Help Center services\nexport const helpCenterService = {\n  getAllTickets: (params) => api.get('/help-center', { params }),\n  getTicket: (id) => api.get(`/help-center/${id}`),\n  createTicket: (ticketData) => api.post('/help-center', ticketData),\n  replyToTicket: (id, replyData) => api.put(`/help-center/${id}/reply`, replyData),\n  updateTicketStatus: (id, statusData) => api.put(`/help-center/${id}/status`, statusData),\n  deleteTicket: (id) => api.delete(`/help-center/${id}`),\n  getStats: () => api.get('/help-center/stats')\n};\n\nexport default api;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB;;AAE7C;AACA,MAAMC,GAAG,GAAGL,KAAK,CAACM,MAAM,CAAC;EACvBC,OAAO,EAAEN,OAAO;EAChBO,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAH,GAAG,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAAC,eAAe,CAAC,GAAG,UAAUK,KAAK,EAAE;EACrD;EACA,OAAOD,MAAM;AACf,CAAC,EACAI,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAX,GAAG,CAACI,YAAY,CAACU,QAAQ,CAACR,GAAG,CAC1BQ,QAAQ,IAAK;EACZ,OAAOA,QAAQ;AACjB,CAAC,EACAH,KAAK,IAAK;EACT,IAAIA,KAAK,CAACG,QAAQ,IAAIH,KAAK,CAACG,QAAQ,CAACC,MAAM,KAAK,GAAG,EAAE;IACnD;IACAN,YAAY,CAACO,UAAU,CAAC,OAAO,CAAC;IAChCP,YAAY,CAACO,UAAU,CAAC,MAAM,CAAC;IAC/BC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;EACjC;EACA,OAAOP,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACA,OAAO,MAAMS,WAAW,GAAG;EACzBC,KAAK,EAAGC,WAAW,IAAKtB,GAAG,CAACuB,IAAI,CAAC,aAAa,EAAED,WAAW,CAAC;EAC5DE,QAAQ,EAAGC,QAAQ,IAAKzB,GAAG,CAACuB,IAAI,CAAC,gBAAgB,EAAEE,QAAQ,CAAC;EAC5DC,UAAU,EAAEA,CAAA,KAAM1B,GAAG,CAAC2B,GAAG,CAAC,UAAU,CAAC;EACrCC,MAAM,EAAEA,CAAA,KAAM5B,GAAG,CAAC2B,GAAG,CAAC,cAAc;AACtC,CAAC;;AAED;AACA,OAAO,MAAME,eAAe,GAAG;EAC7BC,eAAe,EAAEA,CAAA,KAAM9B,GAAG,CAAC2B,GAAG,CAAC,YAAY,CAAC;EAC5CI,WAAW,EAAGC,EAAE,IAAKhC,GAAG,CAAC2B,GAAG,CAAC,cAAcK,EAAE,EAAE,CAAC;EAChDC,cAAc,EAAGC,YAAY,IAAKlC,GAAG,CAACuB,IAAI,CAAC,YAAY,EAAEW,YAAY,CAAC;EACtEC,cAAc,EAAEA,CAACH,EAAE,EAAEE,YAAY,KAAKlC,GAAG,CAACoC,GAAG,CAAC,cAAcJ,EAAE,EAAE,EAAEE,YAAY,CAAC;EAC/EG,cAAc,EAAGL,EAAE,IAAKhC,GAAG,CAACsC,MAAM,CAAC,cAAcN,EAAE,EAAE,CAAC;EACtDO,qBAAqB,EAAEA,CAACP,EAAE,EAAEQ,WAAW,KAAKxC,GAAG,CAACoC,GAAG,CAAC,cAAcJ,EAAE,iBAAiB,EAAE;IAAEQ;EAAY,CAAC;AACxG,CAAC;;AAED;AACA,OAAO,MAAMC,eAAe,GAAG;EAC7BC,eAAe,EAAEA,CAAA,KAAM1C,GAAG,CAAC2B,GAAG,CAAC,YAAY,CAAC;EAC5CgB,WAAW,EAAGX,EAAE,IAAKhC,GAAG,CAAC2B,GAAG,CAAC,cAAcK,EAAE,EAAE,CAAC;EAChDY,cAAc,EAAGC,QAAQ,IAAK7C,GAAG,CAACuB,IAAI,CAAC,YAAY,EAAEsB,QAAQ,EAAE;IAC7D1C,OAAO,EAAE;MACP,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;EACF2C,oBAAoB,EAAEA,CAACd,EAAE,EAAEjB,MAAM,KAAKf,GAAG,CAACoC,GAAG,CAAC,cAAcJ,EAAE,SAAS,EAAE;IAAEjB;EAAO,CAAC,CAAC;EACpFgC,cAAc,EAAEA,CAACf,EAAE,EAAEjB,MAAM,KAAKf,GAAG,CAACoC,GAAG,CAAC,cAAcJ,EAAE,SAAS,EAAE;IAAEjB;EAAO,CAAC,CAAC;EAC9EiC,cAAc,EAAGhB,EAAE,IAAKhC,GAAG,CAACsC,MAAM,CAAC,cAAcN,EAAE,EAAE;AACvD,CAAC;;AAED;AACA,OAAO,MAAMiB,eAAe,GAAG;EAC7BC,eAAe,EAAEA,CAAA,KAAMlD,GAAG,CAAC2B,GAAG,CAAC,YAAY,CAAC;EAC5CwB,WAAW,EAAGnB,EAAE,IAAKhC,GAAG,CAAC2B,GAAG,CAAC,cAAcK,EAAE,EAAE,CAAC;EAChDoB,OAAO,EAAGC,YAAY,IAAKrD,GAAG,CAACuB,IAAI,CAAC,oBAAoB,EAAE8B,YAAY,CAAC;EACvEC,QAAQ,EAAGtB,EAAE,IAAKhC,GAAG,CAACoC,GAAG,CAAC,cAAcJ,EAAE,WAAW,CAAC;EACtDuB,cAAc,EAAGvB,EAAE,IAAKhC,GAAG,CAACsC,MAAM,CAAC,cAAcN,EAAE,EAAE,CAAC;EACtDwB,kBAAkB,EAAEA,CAACxB,EAAE,EAAEqB,YAAY,KAAKrD,GAAG,CAACoC,GAAG,CAAC,cAAcJ,EAAE,cAAc,EAAEqB,YAAY;AAChG,CAAC;;AAED;AACA,OAAO,MAAMI,iBAAiB,GAAG;EAC/BC,aAAa,EAAGC,MAAM,IAAK3D,GAAG,CAAC2B,GAAG,CAAC,cAAc,EAAE;IAAEgC;EAAO,CAAC,CAAC;EAC9DC,SAAS,EAAG5B,EAAE,IAAKhC,GAAG,CAAC2B,GAAG,CAAC,gBAAgBK,EAAE,EAAE,CAAC;EAChD6B,YAAY,EAAGC,UAAU,IAAK9D,GAAG,CAACuB,IAAI,CAAC,cAAc,EAAEuC,UAAU,CAAC;EAClEC,aAAa,EAAEA,CAAC/B,EAAE,EAAEgC,SAAS,KAAKhE,GAAG,CAACoC,GAAG,CAAC,gBAAgBJ,EAAE,QAAQ,EAAEgC,SAAS,CAAC;EAChFC,kBAAkB,EAAEA,CAACjC,EAAE,EAAEkC,UAAU,KAAKlE,GAAG,CAACoC,GAAG,CAAC,gBAAgBJ,EAAE,SAAS,EAAEkC,UAAU,CAAC;EACxFC,YAAY,EAAGnC,EAAE,IAAKhC,GAAG,CAACsC,MAAM,CAAC,gBAAgBN,EAAE,EAAE,CAAC;EACtDoC,QAAQ,EAAEA,CAAA,KAAMpE,GAAG,CAAC2B,GAAG,CAAC,oBAAoB;AAC9C,CAAC;AAED,eAAe3B,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}