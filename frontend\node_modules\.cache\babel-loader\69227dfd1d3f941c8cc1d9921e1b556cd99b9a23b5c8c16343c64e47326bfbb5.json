{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\EmployeeManagementSystem\\\\frontend\\\\src\\\\pages\\\\Dashboard.js\",\n  _s = $RefreshSig$();\nimport { Business as BusinessIcon, CheckCircle as CheckCircleIcon, Description as DocumentIcon, LocationOn as LocationIcon, Pending as PendingIcon, People as PeopleIcon } from '@mui/icons-material';\nimport { Avatar, Box, Card, CardContent, Grid, Typography } from '@mui/material';\nimport { useCallback, useContext, useEffect, useState } from 'react';\nimport { AuthContext } from '../context/AuthContext';\nimport { documentService, employeeService, locationService } from '../services/api';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  const {\n    user,\n    isAdmin\n  } = useContext(AuthContext);\n  const [stats, setStats] = useState({\n    totalEmployees: 0,\n    totalDocuments: 0,\n    pendingDocuments: 0,\n    verifiedDocuments: 0,\n    rejectedDocuments: 0,\n    totalLocations: 0\n  });\n  const [loading, setLoading] = useState(true);\n  const [checkInStatus, setCheckInStatus] = useState({\n    isCheckedIn: false,\n    checkInTime: null,\n    currentLocation: null\n  });\n  const fetchDashboardData = useCallback(async () => {\n    try {\n      setLoading(true);\n      if (isAdmin) {\n        // Fetch admin dashboard data\n        const [employeesRes, documentsRes, locationsRes] = await Promise.all([employeeService.getAllEmployees(), documentService.getAllDocuments(), locationService.getAllLocations()]);\n        const documents = documentsRes.data.data || [];\n        const pendingDocs = documents.filter(doc => doc.verificationStatus === 'pending').length;\n        const verifiedDocs = documents.filter(doc => doc.verificationStatus === 'verified').length;\n        const rejectedDocs = documents.filter(doc => doc.verificationStatus === 'rejected').length;\n        setStats({\n          totalEmployees: employeesRes.data.count || 0,\n          totalDocuments: documents.length,\n          pendingDocuments: pendingDocs,\n          verifiedDocuments: verifiedDocs,\n          rejectedDocuments: rejectedDocs,\n          totalLocations: locationsRes.data.count || 0\n        });\n      } else {\n        // Fetch employee dashboard data\n        const [documentsRes, locationsRes] = await Promise.all([documentService.getAllDocuments(), locationService.getAllLocations()]);\n        const documents = documentsRes.data.data || [];\n        const pendingDocs = documents.filter(doc => doc.verificationStatus === 'pending').length;\n        const verifiedDocs = documents.filter(doc => doc.verificationStatus === 'verified').length;\n        const rejectedDocs = documents.filter(doc => doc.verificationStatus === 'rejected').length;\n\n        // Check if employee is currently checked in\n        const locations = locationsRes.data.data || [];\n        const currentCheckIn = locations.find(loc => loc.employee && loc.employee._id === user.id && loc.checkOutTime === null);\n        setCheckInStatus({\n          isCheckedIn: !!currentCheckIn,\n          checkInTime: (currentCheckIn === null || currentCheckIn === void 0 ? void 0 : currentCheckIn.checkInTime) || null,\n          currentLocation: (currentCheckIn === null || currentCheckIn === void 0 ? void 0 : currentCheckIn.address) || null\n        });\n        setStats({\n          totalEmployees: 0,\n          totalDocuments: documents.length,\n          pendingDocuments: pendingDocs,\n          verifiedDocuments: verifiedDocs,\n          rejectedDocuments: rejectedDocs,\n          totalLocations: locationsRes.data.count || 0\n        });\n      }\n    } catch (error) {\n      console.error('Error fetching dashboard data:', error);\n    } finally {\n      setLoading(false);\n    }\n  }, [isAdmin, user]);\n  useEffect(() => {\n    fetchDashboardData();\n  }, [fetchDashboardData]);\n\n  // Listen for location status changes\n  useEffect(() => {\n    const handleLocationStatusChange = () => {\n      console.log('Location status changed, refreshing dashboard...');\n      fetchDashboardData();\n    };\n\n    // Listen for custom location status change events\n    window.addEventListener('locationStatusChanged', handleLocationStatusChange);\n\n    // Listen for storage changes (for cross-tab communication)\n    const handleStorageChange = e => {\n      if (e.key === 'locationStatusChanged') {\n        handleLocationStatusChange();\n      }\n    };\n    window.addEventListener('storage', handleStorageChange);\n    return () => {\n      window.removeEventListener('locationStatusChanged', handleLocationStatusChange);\n      window.removeEventListener('storage', handleStorageChange);\n    };\n  }, [fetchDashboardData]);\n  const StatCard = ({\n    title,\n    value,\n    icon,\n    color,\n    subtitle\n  }) => /*#__PURE__*/_jsxDEV(Card, {\n    sx: {\n      height: '100%',\n      backgroundColor: '#ffffff',\n      border: '1px solid #e2e8f0',\n      borderRadius: 2,\n      transition: 'all 0.2s ease-in-out',\n      '&:hover': {\n        transform: 'translateY(-2px)',\n        boxShadow: '0 4px 12px rgba(0,0,0,0.1)',\n        borderColor: color\n      }\n    },\n    children: /*#__PURE__*/_jsxDEV(CardContent, {\n      sx: {\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Avatar, {\n          sx: {\n            bgcolor: color,\n            width: 48,\n            height: 48,\n            mr: 2\n          },\n          children: icon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            flex: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              color: '#64748b',\n              fontWeight: 500,\n              mb: 0.5\n            },\n            children: title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              fontWeight: 700,\n              color: '#1e293b',\n              fontSize: '1.25rem'\n            },\n            children: loading ? '...' : value\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"caption\",\n        sx: {\n          color: '#94a3b8'\n        },\n        children: subtitle\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 132,\n    columnNumber: 5\n  }, this);\n  const QuickActionCard = ({\n    title,\n    description,\n    icon,\n    color,\n    action\n  }) => /*#__PURE__*/_jsxDEV(Card, {\n    sx: {\n      height: '100%',\n      cursor: 'pointer',\n      backgroundColor: '#ffffff',\n      border: '1px solid #e2e8f0',\n      borderRadius: 2,\n      transition: 'all 0.2s ease-in-out',\n      '&:hover': {\n        transform: 'translateY(-2px)',\n        boxShadow: '0 4px 12px rgba(0,0,0,0.1)',\n        borderColor: color\n      }\n    },\n    onClick: action,\n    children: /*#__PURE__*/_jsxDEV(CardContent, {\n      sx: {\n        p: 3,\n        textAlign: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Avatar, {\n        sx: {\n          bgcolor: color,\n          color: '#ffffff',\n          width: 56,\n          height: 56,\n          mx: 'auto',\n          mb: 2\n        },\n        children: icon\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        sx: {\n          fontWeight: 600,\n          mb: 1,\n          color: '#1e293b'\n        },\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        sx: {\n          color: '#64748b'\n        },\n        children: description\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 191,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 175,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      width: '100%'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: {\n          xs: 'h5',\n          sm: 'h4'\n        },\n        sx: {\n          fontWeight: 700,\n          color: '#1e293b',\n          mb: 1,\n          fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, sans-serif'\n        },\n        children: [\"Welcome back, \", user === null || user === void 0 ? void 0 : user.name, \"! \\uD83D\\uDC4B\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        sx: {\n          color: '#64748b',\n          fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, sans-serif'\n        },\n        children: isAdmin ? 'Here\\'s what\\'s happening with your team today.' : 'Here\\'s your personal dashboard overview.'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 217,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      sx: {\n        mb: 4\n      },\n      children: [isAdmin && /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Total Employees\",\n          value: stats.totalEmployees,\n          icon: /*#__PURE__*/_jsxDEV(PeopleIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 23\n          }, this),\n          color: \"#1e40af\",\n          subtitle: \"Active staff members\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 242,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Total Documents\",\n          value: stats.totalDocuments,\n          icon: /*#__PURE__*/_jsxDEV(DocumentIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 21\n          }, this),\n          color: \"#059669\",\n          subtitle: isAdmin ? \"All documents\" : \"My documents\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Pending Review\",\n          value: stats.pendingDocuments,\n          icon: /*#__PURE__*/_jsxDEV(PendingIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 21\n          }, this),\n          color: \"#d97706\",\n          subtitle: \"Awaiting approval\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 261,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Verified Documents\",\n          value: stats.verifiedDocuments,\n          icon: /*#__PURE__*/_jsxDEV(CheckCircleIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 21\n          }, this),\n          color: \"#16a34a\",\n          subtitle: \"Approved documents\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 270,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 240,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h5\",\n        sx: {\n          fontWeight: 600,\n          mb: 3,\n          color: '#1e293b',\n          fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, sans-serif'\n        },\n        children: \"Quick Actions\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: isAdmin ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(QuickActionCard, {\n              title: \"Manage Employees\",\n              description: \"Add, edit, or remove employee records\",\n              icon: /*#__PURE__*/_jsxDEV(PeopleIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 27\n              }, this),\n              color: \"#1e40af\",\n              action: () => window.location.href = '/employees'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(QuickActionCard, {\n              title: \"Review Documents\",\n              description: \"Verify and approve employee documents\",\n              icon: /*#__PURE__*/_jsxDEV(DocumentIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 27\n              }, this),\n              color: \"#059669\",\n              action: () => window.location.href = '/documents'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(QuickActionCard, {\n              title: \"Location Tracking\",\n              description: \"Monitor employee locations and check-ins\",\n              icon: /*#__PURE__*/_jsxDEV(LocationIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 27\n              }, this),\n              color: \"#d97706\",\n              action: () => window.location.href = '/locations'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(QuickActionCard, {\n              title: \"Upload Documents\",\n              description: \"Submit your documents for verification\",\n              icon: /*#__PURE__*/_jsxDEV(DocumentIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 27\n              }, this),\n              color: \"#059669\",\n              action: () => window.location.href = '/documents'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(QuickActionCard, {\n              title: checkInStatus.isCheckedIn ? \"Check Out\" : \"Check In\",\n              description: checkInStatus.isCheckedIn ? \"End your work session\" : \"Start your work session\",\n              icon: /*#__PURE__*/_jsxDEV(LocationIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 341,\n                columnNumber: 27\n              }, this),\n              color: checkInStatus.isCheckedIn ? \"#dc2626\" : \"#34d399\",\n              action: () => window.location.href = '/locations'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(QuickActionCard, {\n              title: \"My Profile\",\n              description: \"View and update your profile information\",\n              icon: /*#__PURE__*/_jsxDEV(BusinessIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 350,\n                columnNumber: 27\n              }, this),\n              color: \"#1e40af\",\n              action: () => window.location.href = '/profile'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 295,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 283,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 215,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"5iPuHd7k+6aj1ej6FXazzrSoTAw=\");\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["Business", "BusinessIcon", "CheckCircle", "CheckCircleIcon", "Description", "DocumentIcon", "LocationOn", "LocationIcon", "Pending", "PendingIcon", "People", "PeopleIcon", "Avatar", "Box", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Grid", "Typography", "useCallback", "useContext", "useEffect", "useState", "AuthContext", "documentService", "employeeService", "locationService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Dashboard", "_s", "user", "isAdmin", "stats", "setStats", "totalEmployees", "totalDocuments", "pendingDocuments", "verifiedDocuments", "rejectedDocuments", "totalLocations", "loading", "setLoading", "checkInStatus", "setCheckInStatus", "isCheckedIn", "checkInTime", "currentLocation", "fetchDashboardData", "employeesRes", "documentsRes", "locationsRes", "Promise", "all", "getAllEmployees", "getAllDocuments", "getAllLocations", "documents", "data", "pendingDocs", "filter", "doc", "verificationStatus", "length", "verifiedDocs", "rejectedDocs", "count", "locations", "currentCheckIn", "find", "loc", "employee", "_id", "id", "checkOutTime", "address", "error", "console", "handleLocationStatusChange", "log", "window", "addEventListener", "handleStorageChange", "e", "key", "removeEventListener", "StatCard", "title", "value", "icon", "color", "subtitle", "sx", "height", "backgroundColor", "border", "borderRadius", "transition", "transform", "boxShadow", "borderColor", "children", "p", "display", "alignItems", "mb", "bgcolor", "width", "mr", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "flex", "variant", "fontWeight", "fontSize", "QuickActionCard", "description", "action", "cursor", "onClick", "textAlign", "mx", "xs", "sm", "fontFamily", "name", "container", "spacing", "item", "md", "location", "href", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/EmployeeManagementSystem/frontend/src/pages/Dashboard.js"], "sourcesContent": ["import {\n  Business as BusinessIcon,\n  CheckCircle as CheckCircleIcon,\n  Description as DocumentIcon,\n  LocationOn as LocationIcon,\n  Pending as PendingIcon,\n  People as PeopleIcon\n} from '@mui/icons-material';\nimport {\n  Avatar,\n  Box,\n  Card,\n  CardContent,\n  Grid,\n  Typography\n} from '@mui/material';\nimport { useCallback, useContext, useEffect, useState } from 'react';\nimport { AuthContext } from '../context/AuthContext';\nimport { documentService, employeeService, locationService } from '../services/api';\n\nconst Dashboard = () => {\n  const { user, isAdmin } = useContext(AuthContext);\n  const [stats, setStats] = useState({\n    totalEmployees: 0,\n    totalDocuments: 0,\n    pendingDocuments: 0,\n    verifiedDocuments: 0,\n    rejectedDocuments: 0,\n    totalLocations: 0\n  });\n  const [loading, setLoading] = useState(true);\n  const [checkInStatus, setCheckInStatus] = useState({\n    isCheckedIn: false,\n    checkInTime: null,\n    currentLocation: null\n  });\n\n  const fetchDashboardData = useCallback(async () => {\n    try {\n      setLoading(true);\n\n      if (isAdmin) {\n        // Fetch admin dashboard data\n        const [employeesRes, documentsRes, locationsRes] = await Promise.all([\n          employeeService.getAllEmployees(),\n          documentService.getAllDocuments(),\n          locationService.getAllLocations()\n        ]);\n\n        const documents = documentsRes.data.data || [];\n        const pendingDocs = documents.filter(doc => doc.verificationStatus === 'pending').length;\n        const verifiedDocs = documents.filter(doc => doc.verificationStatus === 'verified').length;\n        const rejectedDocs = documents.filter(doc => doc.verificationStatus === 'rejected').length;\n\n        setStats({\n          totalEmployees: employeesRes.data.count || 0,\n          totalDocuments: documents.length,\n          pendingDocuments: pendingDocs,\n          verifiedDocuments: verifiedDocs,\n          rejectedDocuments: rejectedDocs,\n          totalLocations: locationsRes.data.count || 0\n        });\n      } else {\n        // Fetch employee dashboard data\n        const [documentsRes, locationsRes] = await Promise.all([\n          documentService.getAllDocuments(),\n          locationService.getAllLocations()\n        ]);\n\n        const documents = documentsRes.data.data || [];\n        const pendingDocs = documents.filter(doc => doc.verificationStatus === 'pending').length;\n        const verifiedDocs = documents.filter(doc => doc.verificationStatus === 'verified').length;\n        const rejectedDocs = documents.filter(doc => doc.verificationStatus === 'rejected').length;\n\n        // Check if employee is currently checked in\n        const locations = locationsRes.data.data || [];\n        const currentCheckIn = locations.find(loc =>\n          loc.employee && loc.employee._id === user.id && loc.checkOutTime === null\n        );\n\n        setCheckInStatus({\n          isCheckedIn: !!currentCheckIn,\n          checkInTime: currentCheckIn?.checkInTime || null,\n          currentLocation: currentCheckIn?.address || null\n        });\n\n        setStats({\n          totalEmployees: 0,\n          totalDocuments: documents.length,\n          pendingDocuments: pendingDocs,\n          verifiedDocuments: verifiedDocs,\n          rejectedDocuments: rejectedDocs,\n          totalLocations: locationsRes.data.count || 0\n        });\n      }\n    } catch (error) {\n      console.error('Error fetching dashboard data:', error);\n    } finally {\n      setLoading(false);\n    }\n  }, [isAdmin, user]);\n\n  useEffect(() => {\n    fetchDashboardData();\n  }, [fetchDashboardData]);\n\n  // Listen for location status changes\n  useEffect(() => {\n    const handleLocationStatusChange = () => {\n      console.log('Location status changed, refreshing dashboard...');\n      fetchDashboardData();\n    };\n\n    // Listen for custom location status change events\n    window.addEventListener('locationStatusChanged', handleLocationStatusChange);\n\n    // Listen for storage changes (for cross-tab communication)\n    const handleStorageChange = (e) => {\n      if (e.key === 'locationStatusChanged') {\n        handleLocationStatusChange();\n      }\n    };\n    window.addEventListener('storage', handleStorageChange);\n\n    return () => {\n      window.removeEventListener('locationStatusChanged', handleLocationStatusChange);\n      window.removeEventListener('storage', handleStorageChange);\n    };\n  }, [fetchDashboardData]);\n\n  const StatCard = ({ title, value, icon, color, subtitle }) => (\n    <Card\n      sx={{\n        height: '100%',\n        backgroundColor: '#ffffff',\n        border: '1px solid #e2e8f0',\n        borderRadius: 2,\n        transition: 'all 0.2s ease-in-out',\n        '&:hover': {\n          transform: 'translateY(-2px)',\n          boxShadow: '0 4px 12px rgba(0,0,0,0.1)',\n          borderColor: color,\n        }\n      }}\n    >\n      <CardContent sx={{ p: 3 }}>\n        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n          <Avatar\n            sx={{\n              bgcolor: color,\n              width: 48,\n              height: 48,\n              mr: 2\n            }}\n          >\n            {icon}\n          </Avatar>\n          <Box sx={{ flex: 1 }}>\n            <Typography variant=\"body2\" sx={{ color: '#64748b', fontWeight: 500, mb: 0.5 }}>\n              {title}\n            </Typography>\n            <Typography variant=\"h6\" sx={{ fontWeight: 700, color: '#1e293b', fontSize: '1.25rem' }}>\n              {loading ? '...' : value}\n            </Typography>\n          </Box>\n        </Box>\n        <Typography variant=\"caption\" sx={{ color: '#94a3b8' }}>\n          {subtitle}\n        </Typography>\n      </CardContent>\n    </Card>\n  );\n\n  const QuickActionCard = ({ title, description, icon, color, action }) => (\n    <Card\n      sx={{\n        height: '100%',\n        cursor: 'pointer',\n        backgroundColor: '#ffffff',\n        border: '1px solid #e2e8f0',\n        borderRadius: 2,\n        transition: 'all 0.2s ease-in-out',\n        '&:hover': {\n          transform: 'translateY(-2px)',\n          boxShadow: '0 4px 12px rgba(0,0,0,0.1)',\n          borderColor: color,\n        }\n      }}\n      onClick={action}\n    >\n      <CardContent sx={{ p: 3, textAlign: 'center' }}>\n        <Avatar\n          sx={{\n            bgcolor: color,\n            color: '#ffffff',\n            width: 56,\n            height: 56,\n            mx: 'auto',\n            mb: 2\n          }}\n        >\n          {icon}\n        </Avatar>\n        <Typography variant=\"h6\" sx={{ fontWeight: 600, mb: 1, color: '#1e293b' }}>\n          {title}\n        </Typography>\n        <Typography variant=\"body2\" sx={{ color: '#64748b' }}>\n          {description}\n        </Typography>\n      </CardContent>\n    </Card>\n  );\n\n  return (\n    <Box sx={{ width: '100%' }}>\n      {/* Welcome Section */}\n      <Box sx={{ mb: 4 }}>\n        <Typography\n          variant={{ xs: 'h5', sm: 'h4' }}\n          sx={{\n            fontWeight: 700,\n            color: '#1e293b',\n            mb: 1,\n            fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, sans-serif'\n          }}\n        >\n          Welcome back, {user?.name}! 👋\n        </Typography>\n        <Typography\n          variant=\"body1\"\n          sx={{\n            color: '#64748b',\n            fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, sans-serif'\n          }}\n        >\n          {isAdmin ? 'Here\\'s what\\'s happening with your team today.' : 'Here\\'s your personal dashboard overview.'}\n        </Typography>\n      </Box>\n      {/* Statistics Cards */}\n      <Grid container spacing={3} sx={{ mb: 4 }}>\n          {isAdmin && (\n            <Grid item xs={12} sm={6} md={3}>\n              <StatCard\n                title=\"Total Employees\"\n                value={stats.totalEmployees}\n                icon={<PeopleIcon />}\n                color=\"#1e40af\"\n                subtitle=\"Active staff members\"\n              />\n            </Grid>\n          )}\n          <Grid item xs={12} sm={6} md={3}>\n            <StatCard\n              title=\"Total Documents\"\n              value={stats.totalDocuments}\n              icon={<DocumentIcon />}\n              color=\"#059669\"\n              subtitle={isAdmin ? \"All documents\" : \"My documents\"}\n            />\n          </Grid>\n          <Grid item xs={12} sm={6} md={3}>\n            <StatCard\n              title=\"Pending Review\"\n              value={stats.pendingDocuments}\n              icon={<PendingIcon />}\n              color=\"#d97706\"\n              subtitle=\"Awaiting approval\"\n            />\n          </Grid>\n          <Grid item xs={12} sm={6} md={3}>\n            <StatCard\n              title=\"Verified Documents\"\n              value={stats.verifiedDocuments}\n              icon={<CheckCircleIcon />}\n              color=\"#16a34a\"\n              subtitle=\"Approved documents\"\n            />\n          </Grid>\n\n        </Grid>\n\n      {/* Quick Actions */}\n      <Box sx={{ mb: 4 }}>\n        <Typography\n          variant=\"h5\"\n          sx={{\n            fontWeight: 600,\n            mb: 3,\n            color: '#1e293b',\n            fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, sans-serif'\n          }}\n        >\n          Quick Actions\n        </Typography>\n        <Grid container spacing={3}>\n            {isAdmin ? (\n              <>\n                <Grid item xs={12} sm={6} md={3}>\n                  <QuickActionCard\n                    title=\"Manage Employees\"\n                    description=\"Add, edit, or remove employee records\"\n                    icon={<PeopleIcon />}\n                    color=\"#1e40af\"\n                    action={() => window.location.href = '/employees'}\n                  />\n                </Grid>\n                <Grid item xs={12} sm={6} md={3}>\n                  <QuickActionCard\n                    title=\"Review Documents\"\n                    description=\"Verify and approve employee documents\"\n                    icon={<DocumentIcon />}\n                    color=\"#059669\"\n                    action={() => window.location.href = '/documents'}\n                  />\n                </Grid>\n                <Grid item xs={12} sm={6} md={3}>\n                  <QuickActionCard\n                    title=\"Location Tracking\"\n                    description=\"Monitor employee locations and check-ins\"\n                    icon={<LocationIcon />}\n                    color=\"#d97706\"\n                    action={() => window.location.href = '/locations'}\n                  />\n                </Grid>\n              </>\n            ) : (\n              <>\n                <Grid item xs={12} sm={6} md={4}>\n                  <QuickActionCard\n                    title=\"Upload Documents\"\n                    description=\"Submit your documents for verification\"\n                    icon={<DocumentIcon />}\n                    color=\"#059669\"\n                    action={() => window.location.href = '/documents'}\n                  />\n                </Grid>\n                <Grid item xs={12} sm={6} md={4}>\n                  <QuickActionCard\n                    title={checkInStatus.isCheckedIn ? \"Check Out\" : \"Check In\"}\n                    description={checkInStatus.isCheckedIn ? \"End your work session\" : \"Start your work session\"}\n                    icon={<LocationIcon />}\n                    color={checkInStatus.isCheckedIn ? \"#dc2626\" : \"#34d399\"}\n                    action={() => window.location.href = '/locations'}\n                  />\n                </Grid>\n                <Grid item xs={12} sm={6} md={4}>\n                  <QuickActionCard\n                    title=\"My Profile\"\n                    description=\"View and update your profile information\"\n                    icon={<BusinessIcon />}\n                    color=\"#1e40af\"\n                    action={() => window.location.href = '/profile'}\n                  />\n                </Grid>\n              </>\n            )}\n        </Grid>\n      </Box>\n\n\n    </Box>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,SACEA,QAAQ,IAAIC,YAAY,EACxBC,WAAW,IAAIC,eAAe,EAC9BC,WAAW,IAAIC,YAAY,EAC3BC,UAAU,IAAIC,YAAY,EAC1BC,OAAO,IAAIC,WAAW,EACtBC,MAAM,IAAIC,UAAU,QACf,qBAAqB;AAC5B,SACEC,MAAM,EACNC,GAAG,EACHC,IAAI,EACJC,WAAW,EACXC,IAAI,EACJC,UAAU,QACL,eAAe;AACtB,SAASC,WAAW,EAAEC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AACpE,SAASC,WAAW,QAAQ,wBAAwB;AACpD,SAASC,eAAe,EAAEC,eAAe,EAAEC,eAAe,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpF,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM;IAAEC,IAAI;IAAEC;EAAQ,CAAC,GAAGd,UAAU,CAACG,WAAW,CAAC;EACjD,MAAM,CAACY,KAAK,EAAEC,QAAQ,CAAC,GAAGd,QAAQ,CAAC;IACjCe,cAAc,EAAE,CAAC;IACjBC,cAAc,EAAE,CAAC;IACjBC,gBAAgB,EAAE,CAAC;IACnBC,iBAAiB,EAAE,CAAC;IACpBC,iBAAiB,EAAE,CAAC;IACpBC,cAAc,EAAE;EAClB,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACuB,aAAa,EAAEC,gBAAgB,CAAC,GAAGxB,QAAQ,CAAC;IACjDyB,WAAW,EAAE,KAAK;IAClBC,WAAW,EAAE,IAAI;IACjBC,eAAe,EAAE;EACnB,CAAC,CAAC;EAEF,MAAMC,kBAAkB,GAAG/B,WAAW,CAAC,YAAY;IACjD,IAAI;MACFyB,UAAU,CAAC,IAAI,CAAC;MAEhB,IAAIV,OAAO,EAAE;QACX;QACA,MAAM,CAACiB,YAAY,EAAEC,YAAY,EAAEC,YAAY,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACnE9B,eAAe,CAAC+B,eAAe,CAAC,CAAC,EACjChC,eAAe,CAACiC,eAAe,CAAC,CAAC,EACjC/B,eAAe,CAACgC,eAAe,CAAC,CAAC,CAClC,CAAC;QAEF,MAAMC,SAAS,GAAGP,YAAY,CAACQ,IAAI,CAACA,IAAI,IAAI,EAAE;QAC9C,MAAMC,WAAW,GAAGF,SAAS,CAACG,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACC,kBAAkB,KAAK,SAAS,CAAC,CAACC,MAAM;QACxF,MAAMC,YAAY,GAAGP,SAAS,CAACG,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACC,kBAAkB,KAAK,UAAU,CAAC,CAACC,MAAM;QAC1F,MAAME,YAAY,GAAGR,SAAS,CAACG,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACC,kBAAkB,KAAK,UAAU,CAAC,CAACC,MAAM;QAE1F7B,QAAQ,CAAC;UACPC,cAAc,EAAEc,YAAY,CAACS,IAAI,CAACQ,KAAK,IAAI,CAAC;UAC5C9B,cAAc,EAAEqB,SAAS,CAACM,MAAM;UAChC1B,gBAAgB,EAAEsB,WAAW;UAC7BrB,iBAAiB,EAAE0B,YAAY;UAC/BzB,iBAAiB,EAAE0B,YAAY;UAC/BzB,cAAc,EAAEW,YAAY,CAACO,IAAI,CAACQ,KAAK,IAAI;QAC7C,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA,MAAM,CAAChB,YAAY,EAAEC,YAAY,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACrD/B,eAAe,CAACiC,eAAe,CAAC,CAAC,EACjC/B,eAAe,CAACgC,eAAe,CAAC,CAAC,CAClC,CAAC;QAEF,MAAMC,SAAS,GAAGP,YAAY,CAACQ,IAAI,CAACA,IAAI,IAAI,EAAE;QAC9C,MAAMC,WAAW,GAAGF,SAAS,CAACG,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACC,kBAAkB,KAAK,SAAS,CAAC,CAACC,MAAM;QACxF,MAAMC,YAAY,GAAGP,SAAS,CAACG,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACC,kBAAkB,KAAK,UAAU,CAAC,CAACC,MAAM;QAC1F,MAAME,YAAY,GAAGR,SAAS,CAACG,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACC,kBAAkB,KAAK,UAAU,CAAC,CAACC,MAAM;;QAE1F;QACA,MAAMI,SAAS,GAAGhB,YAAY,CAACO,IAAI,CAACA,IAAI,IAAI,EAAE;QAC9C,MAAMU,cAAc,GAAGD,SAAS,CAACE,IAAI,CAACC,GAAG,IACvCA,GAAG,CAACC,QAAQ,IAAID,GAAG,CAACC,QAAQ,CAACC,GAAG,KAAKzC,IAAI,CAAC0C,EAAE,IAAIH,GAAG,CAACI,YAAY,KAAK,IACvE,CAAC;QAED9B,gBAAgB,CAAC;UACfC,WAAW,EAAE,CAAC,CAACuB,cAAc;UAC7BtB,WAAW,EAAE,CAAAsB,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEtB,WAAW,KAAI,IAAI;UAChDC,eAAe,EAAE,CAAAqB,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEO,OAAO,KAAI;QAC9C,CAAC,CAAC;QAEFzC,QAAQ,CAAC;UACPC,cAAc,EAAE,CAAC;UACjBC,cAAc,EAAEqB,SAAS,CAACM,MAAM;UAChC1B,gBAAgB,EAAEsB,WAAW;UAC7BrB,iBAAiB,EAAE0B,YAAY;UAC/BzB,iBAAiB,EAAE0B,YAAY;UAC/BzB,cAAc,EAAEW,YAAY,CAACO,IAAI,CAACQ,KAAK,IAAI;QAC7C,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOU,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IACxD,CAAC,SAAS;MACRlC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACV,OAAO,EAAED,IAAI,CAAC,CAAC;EAEnBZ,SAAS,CAAC,MAAM;IACd6B,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,CAACA,kBAAkB,CAAC,CAAC;;EAExB;EACA7B,SAAS,CAAC,MAAM;IACd,MAAM2D,0BAA0B,GAAGA,CAAA,KAAM;MACvCD,OAAO,CAACE,GAAG,CAAC,kDAAkD,CAAC;MAC/D/B,kBAAkB,CAAC,CAAC;IACtB,CAAC;;IAED;IACAgC,MAAM,CAACC,gBAAgB,CAAC,uBAAuB,EAAEH,0BAA0B,CAAC;;IAE5E;IACA,MAAMI,mBAAmB,GAAIC,CAAC,IAAK;MACjC,IAAIA,CAAC,CAACC,GAAG,KAAK,uBAAuB,EAAE;QACrCN,0BAA0B,CAAC,CAAC;MAC9B;IACF,CAAC;IACDE,MAAM,CAACC,gBAAgB,CAAC,SAAS,EAAEC,mBAAmB,CAAC;IAEvD,OAAO,MAAM;MACXF,MAAM,CAACK,mBAAmB,CAAC,uBAAuB,EAAEP,0BAA0B,CAAC;MAC/EE,MAAM,CAACK,mBAAmB,CAAC,SAAS,EAAEH,mBAAmB,CAAC;IAC5D,CAAC;EACH,CAAC,EAAE,CAAClC,kBAAkB,CAAC,CAAC;EAExB,MAAMsC,QAAQ,GAAGA,CAAC;IAAEC,KAAK;IAAEC,KAAK;IAAEC,IAAI;IAAEC,KAAK;IAAEC;EAAS,CAAC,kBACvDjE,OAAA,CAACb,IAAI;IACH+E,EAAE,EAAE;MACFC,MAAM,EAAE,MAAM;MACdC,eAAe,EAAE,SAAS;MAC1BC,MAAM,EAAE,mBAAmB;MAC3BC,YAAY,EAAE,CAAC;MACfC,UAAU,EAAE,sBAAsB;MAClC,SAAS,EAAE;QACTC,SAAS,EAAE,kBAAkB;QAC7BC,SAAS,EAAE,4BAA4B;QACvCC,WAAW,EAAEV;MACf;IACF,CAAE;IAAAW,QAAA,eAEF3E,OAAA,CAACZ,WAAW;MAAC8E,EAAE,EAAE;QAAEU,CAAC,EAAE;MAAE,CAAE;MAAAD,QAAA,gBACxB3E,OAAA,CAACd,GAAG;QAACgF,EAAE,EAAE;UAAEW,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,gBACxD3E,OAAA,CAACf,MAAM;UACLiF,EAAE,EAAE;YACFc,OAAO,EAAEhB,KAAK;YACdiB,KAAK,EAAE,EAAE;YACTd,MAAM,EAAE,EAAE;YACVe,EAAE,EAAE;UACN,CAAE;UAAAP,QAAA,EAEDZ;QAAI;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACTtF,OAAA,CAACd,GAAG;UAACgF,EAAE,EAAE;YAAEqB,IAAI,EAAE;UAAE,CAAE;UAAAZ,QAAA,gBACnB3E,OAAA,CAACV,UAAU;YAACkG,OAAO,EAAC,OAAO;YAACtB,EAAE,EAAE;cAAEF,KAAK,EAAE,SAAS;cAAEyB,UAAU,EAAE,GAAG;cAAEV,EAAE,EAAE;YAAI,CAAE;YAAAJ,QAAA,EAC5Ed;UAAK;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eACbtF,OAAA,CAACV,UAAU;YAACkG,OAAO,EAAC,IAAI;YAACtB,EAAE,EAAE;cAAEuB,UAAU,EAAE,GAAG;cAAEzB,KAAK,EAAE,SAAS;cAAE0B,QAAQ,EAAE;YAAU,CAAE;YAAAf,QAAA,EACrF5D,OAAO,GAAG,KAAK,GAAG+C;UAAK;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNtF,OAAA,CAACV,UAAU;QAACkG,OAAO,EAAC,SAAS;QAACtB,EAAE,EAAE;UAAEF,KAAK,EAAE;QAAU,CAAE;QAAAW,QAAA,EACpDV;MAAQ;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CACP;EAED,MAAMK,eAAe,GAAGA,CAAC;IAAE9B,KAAK;IAAE+B,WAAW;IAAE7B,IAAI;IAAEC,KAAK;IAAE6B;EAAO,CAAC,kBAClE7F,OAAA,CAACb,IAAI;IACH+E,EAAE,EAAE;MACFC,MAAM,EAAE,MAAM;MACd2B,MAAM,EAAE,SAAS;MACjB1B,eAAe,EAAE,SAAS;MAC1BC,MAAM,EAAE,mBAAmB;MAC3BC,YAAY,EAAE,CAAC;MACfC,UAAU,EAAE,sBAAsB;MAClC,SAAS,EAAE;QACTC,SAAS,EAAE,kBAAkB;QAC7BC,SAAS,EAAE,4BAA4B;QACvCC,WAAW,EAAEV;MACf;IACF,CAAE;IACF+B,OAAO,EAAEF,MAAO;IAAAlB,QAAA,eAEhB3E,OAAA,CAACZ,WAAW;MAAC8E,EAAE,EAAE;QAAEU,CAAC,EAAE,CAAC;QAAEoB,SAAS,EAAE;MAAS,CAAE;MAAArB,QAAA,gBAC7C3E,OAAA,CAACf,MAAM;QACLiF,EAAE,EAAE;UACFc,OAAO,EAAEhB,KAAK;UACdA,KAAK,EAAE,SAAS;UAChBiB,KAAK,EAAE,EAAE;UACTd,MAAM,EAAE,EAAE;UACV8B,EAAE,EAAE,MAAM;UACVlB,EAAE,EAAE;QACN,CAAE;QAAAJ,QAAA,EAEDZ;MAAI;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACTtF,OAAA,CAACV,UAAU;QAACkG,OAAO,EAAC,IAAI;QAACtB,EAAE,EAAE;UAAEuB,UAAU,EAAE,GAAG;UAAEV,EAAE,EAAE,CAAC;UAAEf,KAAK,EAAE;QAAU,CAAE;QAAAW,QAAA,EACvEd;MAAK;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eACbtF,OAAA,CAACV,UAAU;QAACkG,OAAO,EAAC,OAAO;QAACtB,EAAE,EAAE;UAAEF,KAAK,EAAE;QAAU,CAAE;QAAAW,QAAA,EAClDiB;MAAW;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CACP;EAED,oBACEtF,OAAA,CAACd,GAAG;IAACgF,EAAE,EAAE;MAAEe,KAAK,EAAE;IAAO,CAAE;IAAAN,QAAA,gBAEzB3E,OAAA,CAACd,GAAG;MAACgF,EAAE,EAAE;QAAEa,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACjB3E,OAAA,CAACV,UAAU;QACTkG,OAAO,EAAE;UAAEU,EAAE,EAAE,IAAI;UAAEC,EAAE,EAAE;QAAK,CAAE;QAChCjC,EAAE,EAAE;UACFuB,UAAU,EAAE,GAAG;UACfzB,KAAK,EAAE,SAAS;UAChBe,EAAE,EAAE,CAAC;UACLqB,UAAU,EAAE;QACd,CAAE;QAAAzB,QAAA,GACH,gBACe,EAACtE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgG,IAAI,EAAC,gBAC5B;MAAA;QAAAlB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbtF,OAAA,CAACV,UAAU;QACTkG,OAAO,EAAC,OAAO;QACftB,EAAE,EAAE;UACFF,KAAK,EAAE,SAAS;UAChBoC,UAAU,EAAE;QACd,CAAE;QAAAzB,QAAA,EAEDrE,OAAO,GAAG,iDAAiD,GAAG;MAA2C;QAAA6E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAENtF,OAAA,CAACX,IAAI;MAACiH,SAAS;MAACC,OAAO,EAAE,CAAE;MAACrC,EAAE,EAAE;QAAEa,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,GACrCrE,OAAO,iBACNN,OAAA,CAACX,IAAI;QAACmH,IAAI;QAACN,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACM,EAAE,EAAE,CAAE;QAAA9B,QAAA,eAC9B3E,OAAA,CAAC4D,QAAQ;UACPC,KAAK,EAAC,iBAAiB;UACvBC,KAAK,EAAEvD,KAAK,CAACE,cAAe;UAC5BsD,IAAI,eAAE/D,OAAA,CAAChB,UAAU;YAAAmG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACrBtB,KAAK,EAAC,SAAS;UACfC,QAAQ,EAAC;QAAsB;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACP,eACDtF,OAAA,CAACX,IAAI;QAACmH,IAAI;QAACN,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACM,EAAE,EAAE,CAAE;QAAA9B,QAAA,eAC9B3E,OAAA,CAAC4D,QAAQ;UACPC,KAAK,EAAC,iBAAiB;UACvBC,KAAK,EAAEvD,KAAK,CAACG,cAAe;UAC5BqD,IAAI,eAAE/D,OAAA,CAACtB,YAAY;YAAAyG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBtB,KAAK,EAAC,SAAS;UACfC,QAAQ,EAAE3D,OAAO,GAAG,eAAe,GAAG;QAAe;UAAA6E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACPtF,OAAA,CAACX,IAAI;QAACmH,IAAI;QAACN,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACM,EAAE,EAAE,CAAE;QAAA9B,QAAA,eAC9B3E,OAAA,CAAC4D,QAAQ;UACPC,KAAK,EAAC,gBAAgB;UACtBC,KAAK,EAAEvD,KAAK,CAACI,gBAAiB;UAC9BoD,IAAI,eAAE/D,OAAA,CAAClB,WAAW;YAAAqG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACtBtB,KAAK,EAAC,SAAS;UACfC,QAAQ,EAAC;QAAmB;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACPtF,OAAA,CAACX,IAAI;QAACmH,IAAI;QAACN,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACM,EAAE,EAAE,CAAE;QAAA9B,QAAA,eAC9B3E,OAAA,CAAC4D,QAAQ;UACPC,KAAK,EAAC,oBAAoB;UAC1BC,KAAK,EAAEvD,KAAK,CAACK,iBAAkB;UAC/BmD,IAAI,eAAE/D,OAAA,CAACxB,eAAe;YAAA2G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC1BtB,KAAK,EAAC,SAAS;UACfC,QAAQ,EAAC;QAAoB;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEH,CAAC,eAGTtF,OAAA,CAACd,GAAG;MAACgF,EAAE,EAAE;QAAEa,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACjB3E,OAAA,CAACV,UAAU;QACTkG,OAAO,EAAC,IAAI;QACZtB,EAAE,EAAE;UACFuB,UAAU,EAAE,GAAG;UACfV,EAAE,EAAE,CAAC;UACLf,KAAK,EAAE,SAAS;UAChBoC,UAAU,EAAE;QACd,CAAE;QAAAzB,QAAA,EACH;MAED;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbtF,OAAA,CAACX,IAAI;QAACiH,SAAS;QAACC,OAAO,EAAE,CAAE;QAAA5B,QAAA,EACtBrE,OAAO,gBACNN,OAAA,CAAAE,SAAA;UAAAyE,QAAA,gBACE3E,OAAA,CAACX,IAAI;YAACmH,IAAI;YAACN,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACM,EAAE,EAAE,CAAE;YAAA9B,QAAA,eAC9B3E,OAAA,CAAC2F,eAAe;cACd9B,KAAK,EAAC,kBAAkB;cACxB+B,WAAW,EAAC,uCAAuC;cACnD7B,IAAI,eAAE/D,OAAA,CAAChB,UAAU;gBAAAmG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACrBtB,KAAK,EAAC,SAAS;cACf6B,MAAM,EAAEA,CAAA,KAAMvC,MAAM,CAACoD,QAAQ,CAACC,IAAI,GAAG;YAAa;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPtF,OAAA,CAACX,IAAI;YAACmH,IAAI;YAACN,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACM,EAAE,EAAE,CAAE;YAAA9B,QAAA,eAC9B3E,OAAA,CAAC2F,eAAe;cACd9B,KAAK,EAAC,kBAAkB;cACxB+B,WAAW,EAAC,uCAAuC;cACnD7B,IAAI,eAAE/D,OAAA,CAACtB,YAAY;gBAAAyG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvBtB,KAAK,EAAC,SAAS;cACf6B,MAAM,EAAEA,CAAA,KAAMvC,MAAM,CAACoD,QAAQ,CAACC,IAAI,GAAG;YAAa;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPtF,OAAA,CAACX,IAAI;YAACmH,IAAI;YAACN,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACM,EAAE,EAAE,CAAE;YAAA9B,QAAA,eAC9B3E,OAAA,CAAC2F,eAAe;cACd9B,KAAK,EAAC,mBAAmB;cACzB+B,WAAW,EAAC,0CAA0C;cACtD7B,IAAI,eAAE/D,OAAA,CAACpB,YAAY;gBAAAuG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvBtB,KAAK,EAAC,SAAS;cACf6B,MAAM,EAAEA,CAAA,KAAMvC,MAAM,CAACoD,QAAQ,CAACC,IAAI,GAAG;YAAa;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA,eACP,CAAC,gBAEHtF,OAAA,CAAAE,SAAA;UAAAyE,QAAA,gBACE3E,OAAA,CAACX,IAAI;YAACmH,IAAI;YAACN,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACM,EAAE,EAAE,CAAE;YAAA9B,QAAA,eAC9B3E,OAAA,CAAC2F,eAAe;cACd9B,KAAK,EAAC,kBAAkB;cACxB+B,WAAW,EAAC,wCAAwC;cACpD7B,IAAI,eAAE/D,OAAA,CAACtB,YAAY;gBAAAyG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvBtB,KAAK,EAAC,SAAS;cACf6B,MAAM,EAAEA,CAAA,KAAMvC,MAAM,CAACoD,QAAQ,CAACC,IAAI,GAAG;YAAa;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPtF,OAAA,CAACX,IAAI;YAACmH,IAAI;YAACN,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACM,EAAE,EAAE,CAAE;YAAA9B,QAAA,eAC9B3E,OAAA,CAAC2F,eAAe;cACd9B,KAAK,EAAE5C,aAAa,CAACE,WAAW,GAAG,WAAW,GAAG,UAAW;cAC5DyE,WAAW,EAAE3E,aAAa,CAACE,WAAW,GAAG,uBAAuB,GAAG,yBAA0B;cAC7F4C,IAAI,eAAE/D,OAAA,CAACpB,YAAY;gBAAAuG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvBtB,KAAK,EAAE/C,aAAa,CAACE,WAAW,GAAG,SAAS,GAAG,SAAU;cACzD0E,MAAM,EAAEA,CAAA,KAAMvC,MAAM,CAACoD,QAAQ,CAACC,IAAI,GAAG;YAAa;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPtF,OAAA,CAACX,IAAI;YAACmH,IAAI;YAACN,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACM,EAAE,EAAE,CAAE;YAAA9B,QAAA,eAC9B3E,OAAA,CAAC2F,eAAe;cACd9B,KAAK,EAAC,YAAY;cAClB+B,WAAW,EAAC,0CAA0C;cACtD7B,IAAI,eAAE/D,OAAA,CAAC1B,YAAY;gBAAA6G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvBtB,KAAK,EAAC,SAAS;cACf6B,MAAM,EAAEA,CAAA,KAAMvC,MAAM,CAACoD,QAAQ,CAACC,IAAI,GAAG;YAAW;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA,eACP;MACH;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAGH,CAAC;AAEV,CAAC;AAAClF,EAAA,CAtVID,SAAS;AAAAyG,EAAA,GAATzG,SAAS;AAwVf,eAAeA,SAAS;AAAC,IAAAyG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}